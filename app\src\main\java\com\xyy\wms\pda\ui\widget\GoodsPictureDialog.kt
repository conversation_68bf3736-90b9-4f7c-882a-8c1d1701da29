package com.xyy.wms.pda.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.viewpager2.widget.ViewPager2
import androidx.lifecycle.LifecycleOwner
import com.xyy.wms.pda.R
import com.xyy.wms.pda.ui.adapter.BannerImageAdapter

/**
 * 商品图片轮播弹窗
 */
class GoodsPictureDialog(
    private val mContext: Context,
    private val imageUrls: List<String>
) : Dialog(mContext, R.style.FullScreenDialog) {

    private lateinit var viewPager: ViewPager2
    private lateinit var llIndicators: LinearLayout
    private lateinit var tvCount: TextView
    private lateinit var ivClose: ImageView

    init {
        initDialog()
    }

    private fun initDialog() {
        val view = LayoutInflater.from(mContext).inflate(R.layout.dialog_goods_picture, null)
        setContentView(view)

        initViews(view)
        setupViewPager()
        setupClickListeners()
    }

    private fun initViews(view: View) {
        viewPager = view.findViewById(R.id.viewPager)
        llIndicators = view.findViewById(R.id.ll_indicators)
        tvCount = view.findViewById(R.id.tv_count)
        ivClose = view.findViewById(R.id.iv_close)
    }

    private fun setupViewPager() {
        if (imageUrls.isEmpty()) {
            tvCount.text = "0/0"
            return
        }

        val adapter = BannerImageAdapter(imageUrls)
        viewPager.adapter = adapter

        // 设置页面变化监听器
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateCountText(position + 1)
                updateIndicators(position)
            }
        })

        // 创建指示器
        setupIndicators()

        // 初始化计数显示
        updateCountText(1)
    }

    private fun setupIndicators() {
        llIndicators.removeAllViews()

        for (i in imageUrls.indices) {
            val indicator = View(mContext)
            val params = LinearLayout.LayoutParams(20, 20)
            params.setMargins(8, 0, 8, 0)
            indicator.layoutParams = params

            // 创建圆形背景
            val drawable = GradientDrawable()
            drawable.shape = GradientDrawable.OVAL
            val colorRes = if (i == 0) R.color.theme_green else R.color.text_color_999999
            drawable.setColor(ContextCompat.getColor(mContext, colorRes))
            indicator.background = drawable

            llIndicators.addView(indicator)
        }
    }

    private fun updateIndicators(currentPosition: Int) {
        for (i in 0 until llIndicators.childCount) {
            val indicator = llIndicators.getChildAt(i)
            val colorRes = if (i == currentPosition) R.color.theme_green else R.color.text_color_999999
            val drawable = indicator.background as? GradientDrawable
            drawable?.setColor(ContextCompat.getColor(mContext, colorRes))
        }
    }

    private fun updateCountText(currentPosition: Int) {
        tvCount.text = "$currentPosition/${imageUrls.size}"
    }

    private fun setupClickListeners() {
        ivClose.setOnClickListener {
            dismiss()
        }
    }
}
