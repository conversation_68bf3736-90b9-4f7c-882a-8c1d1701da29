package com.xyy.wms.pda.ui.widget

import android.Manifest
import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.graphics.Rect
import com.xyy.common.util.ToastUtils
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.instorage.BusinessInfo
import com.xyy.wms.pda.bean.instorage.PictureInfo
import com.xyy.wms.pda.bean.instorage.UploadStatus
import com.xyy.wms.pda.presenter.instorage.checkaccept.CheckAcceptBillDetailPresenterNew
import com.xyy.wms.pda.bean.instorage.UploadPicturePost
import com.xyy.wms.pda.manager.ExceptionPictureStateManager
import android.util.Log
import com.xyy.wms.pda.config.ExceptionPictureConfig
import com.xyy.wms.pda.ui.adapter.PictureGridAdapter
import com.xyy.wms.pda.utils.ImageCompressUtils
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图片网格间距装饰器
 */
class GridSpacingItemDecoration(
    private val spanCount: Int,
    private val spacing: Int,
    private val includeEdge: Boolean = true
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val column = position % spanCount

        if (includeEdge) {
            outRect.left = spacing - column * spacing / spanCount
            outRect.right = (column + 1) * spacing / spanCount

            if (position < spanCount) {
                outRect.top = spacing
            }
            outRect.bottom = spacing
        } else {
            outRect.left = column * spacing / spanCount
            outRect.right = spacing - (column + 1) * spacing / spanCount
            if (position >= spanCount) {
                outRect.top = spacing
            }
        }
    }
}

/**
 * 异常图片上传弹窗组件
 * 支持拍照、选择图片、预览、删除和批量上传功能
 */
class ExceptionPictureDialog(context: Context) : Dialog(context, R.style.FullScreenDialog) {

    // Context引用
    private val mContext: Context = context

    // 组件属性
    var maxCount: Int = ExceptionPictureConfig.MAX_PICTURE_COUNT
    var businessInfo: BusinessInfo? = null
    var onPictureUploadListener: ((count: Int) -> Unit)? = null
    var presenter: CheckAcceptBillDetailPresenterNew? = null
    var onPictureStateChangedListener: ((localCount: Int, serverUrls: List<String>) -> Unit)? = null

    // UI组件
    private lateinit var ivBack: ImageView
    private lateinit var tvSubmit: TextView
    private lateinit var tvBusinessCode: TextView
    private lateinit var tvRelatedInfo: TextView
    private lateinit var rvPictures: RecyclerView
    private lateinit var pictureAdapter: PictureGridAdapter

    // 图片数据
    private val pictureList: MutableList<PictureInfo> = mutableListOf()

    // 拍照相关
    private var currentPhotoPath: String = ""
    private val REQUEST_CAMERA_PERMISSION = 1001
    private val REQUEST_STORAGE_PERMISSION = 1002
    private val REQUEST_TAKE_PHOTO = 2001
    private val REQUEST_PICK_IMAGE = 2002

    init {
        initDialog()
    }

    /**
     * 初始化弹窗
     */
    private fun initDialog() {
        Log.d("ExceptionPictureDialog", "initDialog() 开始")
        val view = LayoutInflater.from(mContext).inflate(R.layout.dialog_exception_picture, null)
        setContentView(view)

        // 设置弹窗属性
        window?.setLayout(
            android.view.ViewGroup.LayoutParams.MATCH_PARENT,
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )

        initViews(view)
        setupRecyclerView()
        setupClickListeners()
        updateBusinessInfo()
        Log.d("ExceptionPictureDialog", "initDialog() 完成")
    }

    /**
     * 初始化视图
     */
    private fun initViews(view: View) {
        Log.d("ExceptionPictureDialog", "initViews() 开始")
        ivBack = view.findViewById(R.id.iv_back)
        tvSubmit = view.findViewById(R.id.tv_submit)
        tvBusinessCode = view.findViewById(R.id.tv_business_code)
        tvRelatedInfo = view.findViewById(R.id.tv_related_info)
        rvPictures = view.findViewById(R.id.rv_pictures)

        Log.d("ExceptionPictureDialog", "视图初始化完成 - tvSubmit: ${tvSubmit != null}")
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        pictureAdapter = PictureGridAdapter()

        // 动态计算列数
        val spanCount = calculateSpanCount()
        rvPictures.layoutManager = GridLayoutManager(mContext, spanCount)
        rvPictures.adapter = pictureAdapter

        // 添加间距装饰器
        val spacing = dpToPx(8) // 8dp间距
        rvPictures.addItemDecoration(GridSpacingItemDecoration(spanCount, spacing, true))

        // 设置适配器事件监听
        pictureAdapter.onItemClickListener = { position ->
            showImagePickerDialog()
        }

        pictureAdapter.onDeleteClickListener = { position ->
            showDeleteConfirmDialog(position)
        }

        Log.d("ExceptionPictureDialog", "RecyclerView设置完成")
    }

    /**
     * 动态计算网格列数
     */
    private fun calculateSpanCount(): Int {
        val displayMetrics = mContext.resources.displayMetrics
        val screenWidthPx = displayMetrics.widthPixels
        val paddingPx = dpToPx(32) // 左右各16dp padding
        val availableWidthPx = screenWidthPx - paddingPx

        val minItemWidthPx = dpToPx(80) // 最小图片宽度80dp
        val spacingPx = dpToPx(8) // 间距8dp

        // 计算能容纳的列数
        var spanCount = (availableWidthPx + spacingPx) / (minItemWidthPx + spacingPx)

        // 限制在2-5列之间
        spanCount = spanCount.coerceIn(2, 5)

        return spanCount
    }

    /**
     * dp转px
     */
    private fun dpToPx(dp: Int): Int {
        val density = mContext.resources.displayMetrics.density
        return (dp * density + 0.5f).toInt()
    }

    /**
     * 设置点击事件监听
     */
    private fun setupClickListeners() {
        Log.d("ExceptionPictureDialog", "setupClickListeners() 开始")

        ivBack.setOnClickListener {
            savePictureState()
            dismiss()
        }

        tvSubmit.setOnClickListener {
            Log.d("ExceptionPictureDialog", "提交按钮被点击")
            uploadPictures()
        }

        Log.d("ExceptionPictureDialog", "点击事件设置完成")
    }

    /**
     * 更新业务信息显示
     */
    private fun updateBusinessInfo() {
        businessInfo?.let { info ->
            tvBusinessCode.text = "商品编码：${info.businessCode}"

            // 只显示商品名称作为主要信息
            val productName = info.relatedInfo["商品名称"] ?: ""
            tvRelatedInfo.text = "商品名称：$productName"
        }
    }

    /**
     * 加载图片状态
     */
    private fun loadPictureState() {
        businessInfo?.let { info ->
            Log.d("ExceptionPictureDialog", "加载图片状态，businessId: ${info.businessId}")

            // 从状态管理器获取图片列表
            val savedPictures = ExceptionPictureStateManager.getPictureList(info.businessId)
            Log.d("ExceptionPictureDialog", "从状态管理器获取到 ${savedPictures.size} 张图片")

            // 更新图片列表
            pictureList.clear()
            pictureList.addAll(savedPictures)

            // 确保有足够的占位符
            ensurePlaceholders()

            // 更新适配器数据（updatePictureData内部会调用notifyDataSetChanged）
            Log.d("ExceptionPictureDialog", "更新适配器数据，图片数量: ${pictureList.size}")
            pictureAdapter.updatePictureData(pictureList)

            // 通知状态变化
            notifyPictureStateChanged()
        }
    }

    /**
     * 弹窗显示后刷新状态
     * 在show()后调用，确保回调已经设置
     */
    fun refreshStateAfterShow() {
        Log.d("ExceptionPictureDialog", "弹窗显示后刷新状态")
        loadPictureState()
    }

    /**
     * 保存图片状态
     */
    private fun savePictureState() {
        businessInfo?.let { info ->
            Log.d("ExceptionPictureDialog", "保存图片状态，businessId: ${info.businessId}")

            // 获取实际图片（非占位符）
            val actualPictures = pictureList.filter { !it.isPlaceholder }.toMutableList()

            // 保存到状态管理器
            ExceptionPictureStateManager.setPictureList(info.businessId, actualPictures)

            Log.d("ExceptionPictureDialog", "保存了 ${actualPictures.size} 张图片")
        }
    }

    /**
     * 通知图片状态变化
     */
    private fun notifyPictureStateChanged() {
        businessInfo?.let { info ->
            val localCount = ExceptionPictureStateManager.getLocalPictures(info.businessId).size
            val serverUrls = ExceptionPictureStateManager.getServerPictureUrls(info.businessId)

            Log.d("ExceptionPictureDialog", "图片状态变化 - 本地: $localCount, 服务器: ${serverUrls.size}")

            onPictureStateChangedListener?.invoke(localCount, serverUrls)
            onPictureUploadListener?.invoke(localCount + serverUrls.size)
        }
    }

    /**
     * 确保有足够的占位符
     */
    private fun ensurePlaceholders() {
        val actualPictures = pictureList.filter { !it.isPlaceholder }
        val placeholderCount = maxCount - actualPictures.size

        Log.d("ExceptionPictureDialog", "ensurePlaceholders - 实际图片: ${actualPictures.size}, 需要占位符: $placeholderCount")

        // 移除所有占位符
        pictureList.removeAll { it.isPlaceholder }

        // 添加需要的占位符
        repeat(placeholderCount.coerceAtLeast(1)) {
            pictureList.add(PictureInfo(
                id = "placeholder_${System.currentTimeMillis()}_$it",
                isPlaceholder = true
            ))
        }

        Log.d("ExceptionPictureDialog", "ensurePlaceholders完成 - 总图片数: ${pictureList.size}")
    }

    /**
     * 显示图片选择对话框
     */
    private fun showImagePickerDialog() {
        val options = arrayOf("拍照", "从相册选择")
        AlertDialog.Builder(mContext)
            .setTitle("选择图片")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> checkCameraPermissionAndTakePhoto()
                    1 -> checkStoragePermissionAndPickImage()
                }
            }
            .show()
    }

    /**
     * 检查相机权限并拍照
     */
    private fun checkCameraPermissionAndTakePhoto() {
        if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.CAMERA)
            != PackageManager.PERMISSION_GRANTED) {
            // 请求相机权限
            if (mContext is Activity) {
                ActivityCompat.requestPermissions(
                    mContext,
                    arrayOf(Manifest.permission.CAMERA),
                    REQUEST_CAMERA_PERMISSION
                )
            }
        } else {
            takePhoto()
        }
    }

    /**
     * 检查存储权限并选择图片
     */
    private fun checkStoragePermissionAndPickImage() {
        if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE)
            != PackageManager.PERMISSION_GRANTED) {
            // 请求存储权限
            if (mContext is Activity) {
                ActivityCompat.requestPermissions(
                    mContext,
                    arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE),
                    REQUEST_STORAGE_PERMISSION
                )
            }
        } else {
            pickImageFromGallery()
        }
    }

    /**
     * 拍照
     */
    private fun takePhoto() {
        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (intent.resolveActivity(mContext.packageManager) != null) {
            val photoFile = createImageFile()
            if (photoFile != null) {
                val photoURI = FileProvider.getUriForFile(
                    mContext,
                    "${mContext.packageName}.fileprovider",
                    photoFile
                )
                intent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
                if (mContext is Activity) {
                    mContext.startActivityForResult(intent, REQUEST_TAKE_PHOTO)
                }
            }
        }
    }

    /**
     * 从相册选择图片
     */
    private fun pickImageFromGallery() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        if (mContext is Activity) {
            mContext.startActivityForResult(intent, REQUEST_PICK_IMAGE)
        }
    }

    /**
     * 创建图片文件
     */
    private fun createImageFile(): File? {
        return try {
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFileName = "JPEG_${timeStamp}_"
            val storageDir = mContext.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
            val image = File.createTempFile(imageFileName, ".jpg", storageDir)
            currentPhotoPath = image.absolutePath
            image
        } catch (ex: IOException) {
            null
        }
    }

    /**
     * 处理拍照结果
     */
    fun handleCameraResult() {
        if (currentPhotoPath.isNotEmpty()) {
            addPictureFromPath(currentPhotoPath)
        }
    }

    /**
     * 处理相册选择结果
     */
    fun handleGalleryResult(data: Intent?) {
        Log.d("ExceptionPictureDialog", "handleGalleryResult开始")

        data?.data?.let { uri ->
            Log.d("ExceptionPictureDialog", "获取到URI: $uri")
            val path = getRealPathFromURI(uri)
            Log.d("ExceptionPictureDialog", "转换后的路径: $path")

            if (path != null) {
                addPictureFromPath(path)
            } else {
                Log.e("ExceptionPictureDialog", "无法获取图片路径")
                ToastUtils.showShortSafe("无法获取图片路径")
            }
        } ?: run {
            Log.e("ExceptionPictureDialog", "相册选择数据为空")
        }
    }

    /**
     * 处理权限请求结果
     */
    fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        when (requestCode) {
            REQUEST_CAMERA_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    takePhoto()
                } else {
                    ToastUtils.showShortSafe("需要相机权限才能拍照")
                }
            }
            REQUEST_STORAGE_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    pickImageFromGallery()
                } else {
                    ToastUtils.showShortSafe("需要存储权限才能选择图片")
                }
            }
        }
    }

    /**
     * 从URI获取真实路径
     */
    private fun getRealPathFromURI(uri: Uri): String? {
        return try {
            val cursor = mContext.contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndex(MediaStore.Images.Media.DATA)
                    if (columnIndex >= 0) {
                        it.getString(columnIndex)
                    } else {
                        // 如果无法获取路径，尝试复制文件到临时目录
                        copyUriToTempFile(uri)
                    }
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 复制URI文件到临时目录
     */
    private fun copyUriToTempFile(uri: Uri): String? {
        return try {
            val inputStream = mContext.contentResolver.openInputStream(uri)
            val tempFile = File.createTempFile("temp_image", ".jpg", mContext.cacheDir)
            val outputStream = FileOutputStream(tempFile)

            inputStream?.use { input ->
                outputStream.use { output ->
                    input.copyTo(output)
                }
            }

            tempFile.absolutePath
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 从路径添加图片
     */
    private fun addPictureFromPath(imagePath: String) {
        Log.d("ExceptionPictureDialog", "addPictureFromPath开始，路径: $imagePath")

        if (businessInfo == null) {
            Log.e("ExceptionPictureDialog", "businessInfo为空，无法添加图片")
            return
        }

        businessInfo?.let { info ->
            Log.d("ExceptionPictureDialog", "businessId: ${info.businessId}")

            val currentCount = ExceptionPictureStateManager.getPictureCount(info.businessId)
            Log.d("ExceptionPictureDialog", "当前图片数量: $currentCount, 最大数量: $maxCount")

            if (currentCount >= maxCount) {
                ToastUtils.showShortSafe("最多只能上传${maxCount}张图片")
                return
            }

            // 压缩图片
            val compressedPath = try {
                Log.d("ExceptionPictureDialog", "开始压缩图片")
                val result = ImageCompressUtils.compressImage(imagePath, ExceptionPictureConfig.COMPRESS_QUALITY)
                Log.d("ExceptionPictureDialog", "图片压缩完成: $result")
                result
            } catch (e: Exception) {
                Log.w("ExceptionPictureDialog", "图片压缩失败，使用原图片: ${e.message}")
                imagePath
            }

            // 检查文件大小
            val file = File(compressedPath ?: imagePath)
            Log.d("ExceptionPictureDialog", "文件大小: ${file.length()} bytes")

            if (file.length() > ExceptionPictureConfig.MAX_FILE_SIZE) {
                ToastUtils.showShortSafe("图片大小不能超过5MB")
                return
            }

            // 添加到状态管理器
            Log.d("ExceptionPictureDialog", "添加图片到状态管理器")
            val pictureInfo = ExceptionPictureStateManager.addLocalPicture(info.businessId, compressedPath ?: imagePath)

            Log.d("ExceptionPictureDialog", "添加本地图片成功: ${pictureInfo.id}, 路径: ${pictureInfo.localPath}")

            // 重新加载图片状态
            Log.d("ExceptionPictureDialog", "重新加载图片状态")
            loadPictureState()

            Log.d("ExceptionPictureDialog", "addPictureFromPath完成")
        }
    }

    /**
     * 显示删除确认对话框
     */
    private fun showDeleteConfirmDialog(position: Int) {
        AlertDialog.Builder(mContext)
            .setTitle("确认删除")
            .setMessage("确定要删除这张图片吗？")
            .setPositiveButton("删除") { _, _ ->
                deletePicture(position)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 删除图片
     */
    private fun deletePicture(position: Int) {
        businessInfo?.let { info ->
            if (position < pictureList.size) {
                val pictureInfo = pictureList[position]
                if (!pictureInfo.isPlaceholder) {
                    Log.d("ExceptionPictureDialog", "删除图片: ${pictureInfo.id}")

                    // 从状态管理器删除
                    ExceptionPictureStateManager.removePicture(info.businessId, pictureInfo.id)

                    // 重新加载图片状态
                    loadPictureState()
                }
            }
        }
    }

    /**
     * 上传图片
     */
    private fun uploadPictures() {
        Log.d("ExceptionPictureDialog", "uploadPictures() 开始执行")

        val actualPictures = pictureAdapter.getActualPictures()
        Log.d("ExceptionPictureDialog", "获取到的图片数量: ${actualPictures.size}")

        if (actualPictures.isEmpty()) {
            Log.w("ExceptionPictureDialog", "没有图片，显示提示")
            ToastUtils.showShortSafe("请先选择图片")
            return
        }

        // 检查是否有Presenter
        if (presenter == null) {
            Log.e("ExceptionPictureDialog", "Presenter为空")
            ToastUtils.showShortSafe("上传失败：缺少必要组件")
            return
        }
        Log.d("ExceptionPictureDialog", "Presenter检查通过")

        // 检查业务信息
        if (businessInfo == null) {
            Log.e("ExceptionPictureDialog", "BusinessInfo为空")
            ToastUtils.showShortSafe("上传失败：缺少业务信息")
            return
        }
        Log.d("ExceptionPictureDialog", "BusinessInfo检查通过: ${businessInfo?.businessId}")

        try {
            // 根据文档格式构建上传请求
            Log.d("ExceptionPictureDialog", "开始构建fileMap")
            val fileMap = mutableMapOf<Int, MutableList<PictureInfo>>()
            fileMap[1] = actualPictures.toMutableList() // 使用类型1表示异常图片
            Log.d("ExceptionPictureDialog", "fileMap构建完成，大小: ${fileMap.size}")

            Log.d("ExceptionPictureDialog", "开始创建UploadPicturePost")
            val uploadRequest = try {
                UploadPicturePost(fileMap)
            } catch (e: Exception) {
                Log.e("ExceptionPictureDialog", "创建UploadPicturePost失败: ${e.message}", e)
                throw e
            }
            Log.d("ExceptionPictureDialog", "UploadPicturePost创建完成")

            // 调用上传接口
            Log.d("ExceptionPictureDialog", "调用presenter.uploadPicture，presenter类型: ${presenter?.javaClass?.simpleName}")
            Log.d("ExceptionPictureDialog", "presenter是否为空: ${presenter == null}")

            if (presenter != null) {
                Log.d("ExceptionPictureDialog", "开始调用presenter.uploadPicture方法")
                try {
                    presenter!!.uploadPicture(uploadRequest)
                    Log.d("ExceptionPictureDialog", "presenter.uploadPicture方法调用成功")
                } catch (e: Exception) {
                    Log.e("ExceptionPictureDialog", "调用presenter.uploadPicture失败: ${e.message}", e)
                    throw e
                }
            } else {
                Log.e("ExceptionPictureDialog", "presenter为空，无法调用uploadPicture")
            }

            Log.d("ExceptionPictureDialog", "presenter.uploadPicture调用完成")

            // 保存状态并关闭弹窗
            savePictureState()
            dismiss()

        } catch (e: Exception) {
            Log.e("ExceptionPictureDialog", "上传异常: ${e.message}", e)
            ToastUtils.showShortSafe("上传失败：${e.message}")
        }
    }

    /**
     * 处理上传成功回调
     * @param serverUrls 服务器返回的图片URL列表
     */
    fun handleUploadSuccess(serverUrls: List<String>) {
        businessInfo?.let { info ->
            Log.d("ExceptionPictureDialog", "上传成功，收到 ${serverUrls.size} 个URL")

            // 同步服务器图片到状态管理器
            ExceptionPictureStateManager.syncServerPictures(info.businessId, serverUrls)

            // 清理本地图片（已上传成功）
            ExceptionPictureStateManager.clearLocalPictures(info.businessId)

            // 通知状态变化
            notifyPictureStateChanged()

            Log.d("ExceptionPictureDialog", "图片状态同步完成")
        }
    }
}
