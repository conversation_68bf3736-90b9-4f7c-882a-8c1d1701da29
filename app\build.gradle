apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'walle'
//apply plugin: 'xyy-apm-plugin'

android {
    sourceSets.main.java.srcDirs += 'src/main/kotlin'
    sourceSets.main.java.srcDirs += 'src/main/mvvm'

    buildFeatures {
        dataBinding = true
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    compileSdkVersion rootProject.ext.android.compileSdkVersion

    defaultConfig {
        applicationId rootProject.ext.android.applicationId
        minSdkVersion rootProject.ext.android.minSdkVersion
        targetSdkVersion rootProject.ext.android.targetSdkVersion
        versionCode project.hasProperty("versionCode") ? Integer.parseInt(project.versionCode) : rootProject.ext.android.versionCode
        versionName project.hasProperty("versionName") ? project.versionName : rootProject.ext.android.versionName
        manifestPlaceholders = [UMENG_CHANNEL_VALUE: "default"]

        multiDexKeepProguard file("multiDexKeep.pro") //分包时，将部分包放入主dex

        ndk {
            // 设置Bugly和极光支持的SO库架构
            abiFilters 'armeabi', 'x86', 'armeabi-v7a'//, 'x86_64', 'arm64-v8a'
        }
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }

        sourceSets {
            main {
                assets.srcDirs = ['src/debug/assets']
            }
        }
    }
    signingConfigs {
        //开发者签名
        production {
            storeFile file("keystore/XyyPda.jks")
            storePassword "android"
            keyAlias "pdaKeyStore"
            keyPassword "android"
        }
    }
    buildTypes {
        debug {
            manifestPlaceholders = [
                    app_name      : "百草盒子测试",
                    application_id: rootProject.ext.android.applicationTestId,
            ]
            applicationIdSuffix '.debug'
            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationTestId + ".fileprovider" + '\"')
            //不混淆
            minifyEnabled false
            //Zipalign优化
            zipAlignEnabled true
            signingConfig signingConfigs.production
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        release {
            manifestPlaceholders = [
                    app_name      : "百草盒子",
                    application_id: rootProject.ext.android.applicationTestId,
            ]
            buildConfigField("String", "PROVIDER_NAME", '\"' + rootProject.ext.android.applicationId + ".fileprovider" + '\"')
            //混淆
            minifyEnabled true
            debuggable true // 允许调试
            //Zipalign优化
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources true
            signingConfig signingConfigs.production
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            //批量修改Apk名字
            applicationVariants.all { variant ->
                variant.outputs.all { output ->
                    outputFileName = "xyy-pda-${variant.name}-${defaultConfig.versionName}-${defaultConfig.versionCode}.apk"
                }
            }
        }
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    walle {
        // 指定渠道包的输出路径
        apkOutputFolder = new File("${project.buildDir}/outputs/channels");
        // 定制渠道包的APK的文件名称
        apkFileNameFormat = '${appName}-${packageName}-${channel}-${buildType}-v${versionName}-${versionCode}-${buildTime}.apk';
        // 渠道配置文件
        channelFile = new File("${project.getProjectDir()}/channel")
    }
}

dependencies {
    implementation files('libs/iscanuserapi.jar')
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    //依赖Module
    implementation project(path: ':utilslibrary')
    implementation project(path: ':common')
    implementation project(path: ':tablayout')

    implementation rootProject.ext.dependencies.appcompat
    implementation rootProject.ext.dependencies.material
    implementation rootProject.ext.dependencies.constraintLayout

    implementation "androidx.activity:activity-ktx:$activityVersion"

    // Bugly集成SDK和NDK
    //noinspection GradleDependency
    implementation "com.tencent.bugly:crashreport:$buglyVersion"
    //RxPermissions
    implementation "com.tbruyelle.rxpermissions2:rxpermissions:$rxpermissionsVersion"
    //Butterknife
    implementation "com.jakewharton:butterknife:$butterknifeVersion"
    kapt "com.jakewharton:butterknife-compiler:$butterknifeVersion"
    // Retrofit
    implementation "com.squareup.retrofit2:retrofit:$retrofitVersion"
    implementation "com.squareup.retrofit2:converter-gson:$retrofitVersion"
    implementation "com.squareup.retrofit2:adapter-rxjava2:$retrofitAdapterVersion"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttpLogVersion"
    implementation "com.squareup.okhttp3:okhttp:$okhttpVersion"
    // RxJava
    implementation "io.reactivex.rxjava2:rxjava:$rxjavaVersion"
    implementation "io.reactivex.rxjava2:rxandroid:$rxandroidVersion"
    implementation "com.jakewharton.rxbinding3:rxbinding:$rxbindingVersion"
    implementation "com.jakewharton.rxrelay2:rxrelay:$rxrelayVersion"
    //https://github.com/CymChad/BaseRecyclerViewAdapterHelper/
    implementation "com.github.CymChad:BaseRecyclerViewAdapterHelper:$baseAdapterVersion"
    //https://github.com/Bigkoo/Android-PickerView 选择器
    implementation "com.contrarywind:Android-PickerView:$pickerViewVersion"
    //gson
    implementation "com.google.code.gson:gson:$gsonVersion"
    // kotlin 反射
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"

    // ExifInterface for image rotation handling
    implementation 'androidx.exifinterface:exifinterface:1.3.6'

    //ARouter
    implementation "com.alibaba:arouter-api:$arouterVersion"
    kapt "com.alibaba:arouter-compiler:$arouterKaptVersion"
    // liveDataBus
    implementation "com.github.lwjfork:livedata-bus:$livedataVersion"

    implementation 'com.meituan.android.walle:library:1.1.7'

    implementation 'com.github.bumptech.glide:glide:4.9.0'


    implementation 'com.github.chrisbanes:PhotoView:2.0.0'

    // ViewPager2 (已包含在androidx中，无需额外依赖)

    // apm 相关的依赖
//    implementation "com.xyy.apm:start:$xyyApmVersion"

    // 友盟
//    implementation "com.umeng.umsdk:common:9.3.3" //（必选）
//    implementation 'com.umeng.umsdk:asms:1.1.4' // asms包依赖(必选)
//    implementation 'com.umeng.umsdk:apm:1.1.0' // U-APM产品包依赖(必选)

    // 调试数据时使用
//    debugImplementation "com.glance.guolindev:glance:1.0.0-alpha02"
}

// 依赖缓存时间设置
configurations.all {
    // 项目中外部依赖的缓存时间默认为 24 小时，但开发阶段 APM 相关的 Jar 包更新较为频。
    // 此处将缓存时间设置为 0 秒，即每次构建时均从远端拉取最新的 Jar 包，便于测试。
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

//xyyApmConfig {
//    // 测试环境,上线时候需要注释掉
//    if (!isReleaseBuildType()) {
//        baseUrl = 'http://wms-apm.test.ybm100.com/app/'
//    }
//    // 开启代码织入
//    enable = true
//}


boolean isReleaseBuildType() {
    for (String s : gradle.startParameter.taskNames) {
        if (s.contains("Release") | s.contains("release")) {
            return true
        }
    }
    return false
}

