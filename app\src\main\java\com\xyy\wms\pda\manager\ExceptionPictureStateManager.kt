package com.xyy.wms.pda.manager

import com.xyy.wms.pda.bean.instorage.PictureInfo
import com.xyy.wms.pda.bean.instorage.UploadStatus
import java.util.*

/**
 * 异常图片状态管理器
 * 负责管理不同商品的图片状态，包括本地图片和服务器图片
 */
object ExceptionPictureStateManager {
    
    // 商品维度的图片状态缓存 Key: businessId, Value: 图片列表
    private val productPictureCache = mutableMapOf<String, MutableList<PictureInfo>>()
    
    /**
     * 获取指定商品的图片列表
     * @param businessId 业务ID（验收单号_商品编码）
     * @return 图片列表的副本
     */
    fun getPictureList(businessId: String): MutableList<PictureInfo> {
        return productPictureCache[businessId]?.map { it.copy() }?.toMutableList() ?: mutableListOf()
    }
    
    /**
     * 设置指定商品的图片列表
     * @param businessId 业务ID
     * @param pictures 图片列表
     */
    fun setPictureList(businessId: String, pictures: MutableList<PictureInfo>) {
        productPictureCache[businessId] = pictures.map { it.copy() }.toMutableList()
    }
    
    /**
     * 添加本地图片到指定商品
     * @param businessId 业务ID
     * @param localPath 本地图片路径
     * @return 新添加的图片信息
     */
    fun addLocalPicture(businessId: String, localPath: String): PictureInfo {
        val pictureList = productPictureCache.getOrPut(businessId) { mutableListOf() }
        
        val pictureInfo = PictureInfo(
            id = UUID.randomUUID().toString(),
            localPath = localPath,
            isLocal = true,
            isPlaceholder = false,
            uploadStatus = UploadStatus.NONE,
            fileSize = java.io.File(localPath).length(),
            createTime = System.currentTimeMillis()
        )
        
        pictureList.add(pictureInfo)
        return pictureInfo
    }
    
    /**
     * 从服务器URL列表同步图片信息
     * @param businessId 业务ID
     * @param serverUrls 服务器图片URL列表
     */
    fun syncServerPictures(businessId: String, serverUrls: List<String>) {
        val pictureList = productPictureCache.getOrPut(businessId) { mutableListOf() }
        
        // 移除已经不存在的服务器图片
        pictureList.removeAll { !it.isLocal && !serverUrls.contains(it.serverUrl) }
        
        // 添加新的服务器图片
        serverUrls.forEach { url ->
            val existingPicture = pictureList.find { it.serverUrl == url }
            if (existingPicture == null) {
                val pictureInfo = PictureInfo(
                    id = UUID.randomUUID().toString(),
                    serverUrl = url,
                    isLocal = false,
                    isPlaceholder = false,
                    uploadStatus = UploadStatus.SUCCESS,
                    createTime = System.currentTimeMillis()
                )
                pictureList.add(pictureInfo)
            }
        }
    }
    
    /**
     * 删除指定图片
     * @param businessId 业务ID
     * @param pictureId 图片ID
     */
    fun removePicture(businessId: String, pictureId: String) {
        productPictureCache[businessId]?.removeAll { it.id == pictureId }
    }
    
    /**
     * 清理指定商品的本地图片（保留服务器图片）
     * @param businessId 业务ID
     */
    fun clearLocalPictures(businessId: String) {
        productPictureCache[businessId]?.removeAll { it.isLocal }
    }
    
    /**
     * 清理指定商品的所有图片
     * @param businessId 业务ID
     */
    fun clearAllPictures(businessId: String) {
        productPictureCache.remove(businessId)
    }
    
    /**
     * 获取指定商品的图片总数
     * @param businessId 业务ID
     * @return 图片总数
     */
    fun getPictureCount(businessId: String): Int {
        return productPictureCache[businessId]?.size ?: 0
    }
    
    /**
     * 获取指定商品的本地图片列表
     * @param businessId 业务ID
     * @return 本地图片列表
     */
    fun getLocalPictures(businessId: String): List<PictureInfo> {
        return productPictureCache[businessId]?.filter { it.isLocal } ?: emptyList()
    }
    
    /**
     * 获取指定商品的服务器图片URL列表
     * @param businessId 业务ID
     * @return 服务器图片URL列表
     */
    fun getServerPictureUrls(businessId: String): List<String> {
        return productPictureCache[businessId]?.filter { !it.isLocal && it.serverUrl.isNotEmpty() }?.map { it.serverUrl } ?: emptyList()
    }
    
    /**
     * 更新图片上传状态
     * @param businessId 业务ID
     * @param pictureId 图片ID
     * @param status 上传状态
     * @param serverUrl 服务器URL（上传成功时）
     */
    fun updateUploadStatus(businessId: String, pictureId: String, status: UploadStatus, serverUrl: String = "") {
        productPictureCache[businessId]?.find { it.id == pictureId }?.let { picture ->
            picture.uploadStatus = status
            if (status == UploadStatus.SUCCESS && serverUrl.isNotEmpty()) {
                picture.serverUrl = serverUrl
                picture.isLocal = false
            }
        }
    }
}
