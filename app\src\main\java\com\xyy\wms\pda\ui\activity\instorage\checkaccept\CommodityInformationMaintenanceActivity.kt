package com.xyy.wms.pda.ui.activity.instorage.checkaccept

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lwjfork.bus.LiveDataBus
import com.scwang.smartrefresh.layout.util.DensityUtil
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.wms.pda.R
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.constant.BusCode
import com.xyy.wms.pda.utils.CommonInfoUtils
import com.xyy.wms.pad.instorage.presenter.newinspection.CommodityInformationMaintenancePresenter
import com.xyy.wms.pda.bean.instorage.checkaccept.AbcTypeList
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.GetUpperLowerProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.LargeProductPackingVO
import com.xyy.wms.pda.bean.instorage.checkaccept.LhkLogicalregionRelation
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainBean
import com.xyy.wms.pda.bean.instorage.checkaccept.LogicalRegionProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryBean
import com.xyy.wms.pda.bean.instorage.checkaccept.ProductInfoMaintainQueryPost
import com.xyy.wms.pda.bean.instorage.checkaccept.SaveProductInfoMaintainPost
import com.xyy.wms.pda.bean.instorage.checkaccept.SmallProductPackingVO
import com.xyy.wms.pda.bean.instorage.checkaccept.StorageAttributeList
import com.xyy.wms.pda.bean.instorage.checkaccept.ZjkLogicalregionRelation
import com.xyy.wms.pda.contract.instorage.checkaccept.CommodityInformationMaintenanceContract
import com.xyy.wms.pda.ui.adapter.instorage.checkaccept.CommodityMaintenanceAdapter
import com.xyy.wms.pda.utils.PickerDialogManager
import kotlinx.android.synthetic.main.operate_layout.tv_examiner
import kotlinx.android.synthetic.main.operate_layout.tv_examiner_key
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time_key
import kotlinx.android.synthetic.main.activity_check_bill_base_info_new.*
/**
 * 入库验收单-基础资料维护
 */
class CommodityInformationMaintenanceActivity : BaseMVPCompatActivity<CommodityInformationMaintenancePresenter>(), CommodityInformationMaintenanceContract.CommodityInformationMaintenanceView {

    private var productInfoMaintainQueryBean: ProductInfoMaintainQueryBean? = null
    private lateinit var productCode: String
    private var ifErrorProne: String? = null
    private var isUpdatingFields = false // 标志位，防止程序填充数据时触发联动计算
    private var isSmall: Boolean = true // 当前是否为小包装模式

    // 存储属性和ABC类型相关
    private var storageAttribute: StorageAttributeList? = null
    private var storageAttributeList: ArrayList<PickerDialogManager.SelectItemModel<StorageAttributeList>> = arrayListOf()
    private var abcTypeList: ArrayList<PickerDialogManager.SelectItemModel<AbcTypeList>> = arrayListOf()
    private var wholeAbcTypeList: ArrayList<PickerDialogManager.SelectItemModel<AbcTypeList>> = arrayListOf()

    // 包装信息
    private var smallProductPackingVO: SmallProductPackingVO? = null
    private var largeProductPackingVO: LargeProductPackingVO? = null
    private var lhkLogicalregionRelation: LhkLogicalregionRelation? = null
    private var zjkLogicalregionRelation: ZjkLogicalregionRelation? = null

    // 逻辑区域相关
    private lateinit var dialog_commodity_maintenance: Dialog
    private lateinit var dialog_whole_commodity_maintenance: Dialog
    private var commodityMaintenanceAdapter: CommodityMaintenanceAdapter? = null
    private var wholeCommodityMaintenanceAdapter: CommodityMaintenanceAdapter? = null
    private var commodityMaintenanceList: java.util.ArrayList<LogicalRegionProductInfoMaintainBean> = arrayListOf()
    private var wholeCommodityMaintenanceList: java.util.ArrayList<LogicalRegionProductInfoMaintainBean> = arrayListOf()

    /**
     * 初始化传递的数据
     */
    override fun initTransferData() {
        super.initTransferData()
        intent?.let {
            productCode = it.getStringExtra("productCode") ?: ""
        }
    }

    /**
     * 初始化Presenter
     */
    override fun initPresenter(): BasePresenter<*, *> {
        return CommodityInformationMaintenancePresenter.newInstance()
    }

    /**
     * 获取布局ID
     */
    override fun getLayoutId(): Int {
        return R.layout.activity_check_bill_base_info_new
    }

    /**
     * 初始化视图
     */
    override fun initView(savedInstanceState: Bundle?) {
        setupOperatorLayout()
        addListener()
        getData()
        initCommodityMaintenanceDialog()
        initWholeCommodityMaintenanceDialog()

        LiveDataBus.get().with(BusCode.REFRESH_LIST).observe(this, Observer {})
    }

    /**
     * 设置操作员布局
     */
    private fun setupOperatorLayout() {
        initTitleBar(toolbar_check_accept, "")
        CommonInfoUtils.setOperationName(tv_examiner_key, "操作员：", tv_examiner)
        CommonInfoUtils.setOperationTime(tv_reception_time_key, "操作时间：", tv_reception_time)
    }

    /**
     * 添加监听器
     */
    private fun addListener() {
        // 逻辑区域点击
        et_logical_area.setOnClickListener {
            if (commodityMaintenanceList.size <= 0) {
                showToast("当前没有逻辑区域")
                return@setOnClickListener
            }
            dialog_commodity_maintenance.show()
        }

        et_whole_logical_area.setOnClickListener {
            if (wholeCommodityMaintenanceList.size <= 0) {
                showToast("当前没有逻辑区域")
                return@setOnClickListener
            }
            dialog_whole_commodity_maintenance.show()
        }

        // 存储属性点击
        et_storage_properties.setOnClickListener { showStorageAttributeListDialog() }

        // ABC分类点击
        et_product_classification.setOnClickListener { showABCTypeListDialog() }
        et_whole_product_classification.setOnClickListener { showWholeABCTypeListDialog() }

        // 易收错选择
        rb_is_receive_yes.setOnClickListener { ifErrorProne = "1" }
        rb_is_receive_no.setOnClickListener { ifErrorProne = "0" }

        // 提交按钮
        btn_submit.setOnClickListener { submitData() }

        // 添加联动计算监听器
        setupCalculationListeners()
    }

    /**
     * 设置联动计算监听器
     */
    private fun setupCalculationListeners() {
        // 小包装长度监听器
        et_small_packet_length.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = true
                calVolume()
                smallProductPackingVO?.packingLong = p0.toString().toFloat()
            }
        })

        // 小包装宽度监听器
        et_small_packet_width.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = true
                calVolume()
                smallProductPackingVO?.packingWide = p0.toString().toFloat()
            }
        })

        // 小包装高度监听器
        et_small_packet_height.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = true
                calVolume()
                smallProductPackingVO?.packingHigh = p0.toString().toFloat()
            }
        })

        // 大包装长度监听器
        et_large_packet_length.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = false
                calVolume()
                largeProductPackingVO?.packingLong = p0.toString().toFloat()
            }
        })

        // 大包装宽度监听器
        et_large_packet_width.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = false
                calVolume()
                largeProductPackingVO?.packingWide = p0.toString().toFloat()
            }
        })

        // 大包装高度监听器
        et_large_packet_height.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                isSmall = false
                calVolume()
                largeProductPackingVO?.packingHigh = p0.toString().toFloat()
            }
        })

        // 小包装体积监听器
        et_small_packet_volume.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                smallProductPackingVO?.packingVolume = p0.toString().toFloat()
                // 按照copy文件逻辑：使用largeProductPackingVO?.packingNumber
                try {
                    val newLargeVolume = (smallProductPackingVO?.packingVolume!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
                    largeProductPackingVO?.packingVolume = newLargeVolume
                    // 更新大包装体积显示
                    isUpdatingFields = true
                    et_large_packet_volume.setText(newLargeVolume.toString())
                    isUpdatingFields = false
                } catch (e: Exception) {
                    // 忽略计算错误
                }
            }
        })

        // 大包装体积监听器
        et_large_packet_volume.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                largeProductPackingVO?.packingVolume = p0.toString().toFloat()
                // 按照copy文件逻辑：使用largeProductPackingVO?.packingNumber
                try {
                    val newSmallVolume = (largeProductPackingVO?.packingVolume!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
                    smallProductPackingVO?.packingVolume = newSmallVolume
                    // 更新小包装体积显示
                    isUpdatingFields = true
                    et_small_packet_volume.setText(newSmallVolume.toString())
                    isUpdatingFields = false
                } catch (e: Exception) {
                    // 忽略计算错误
                }
            }
        })

        // 小包装重量监听器
        et_small_packet_weight.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                smallProductPackingVO?.packingWeight = p0.toString().toFloat()
                // 按照copy文件逻辑：使用largeProductPackingVO?.packingNumber
                try {
                    val newLargeWeight = (smallProductPackingVO?.packingWeight!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
                    largeProductPackingVO?.packingWeight = newLargeWeight
                    // 更新大包装重量显示
                    isUpdatingFields = true
                    et_large_packet_weight.setText(newLargeWeight.toString())
                    isUpdatingFields = false
                } catch (e: Exception) {
                    // 忽略计算错误
                }
            }
        })

        // 大包装重量监听器
        et_large_packet_weight.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {}
            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "" || isUpdatingFields) return
                largeProductPackingVO?.packingWeight = p0.toString().toFloat()
                // 按照copy文件逻辑：使用largeProductPackingVO?.packingNumber
                try {
                    val newSmallWeight = (largeProductPackingVO?.packingWeight!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
                    smallProductPackingVO?.packingWeight = newSmallWeight
                    // 更新小包装重量显示
                    isUpdatingFields = true
                    et_small_packet_weight.setText(newSmallWeight.toString())
                    isUpdatingFields = false
                } catch (e: Exception) {
                    // 忽略计算错误
                }
            }
        })
    }

    /**
     * 自动计算体积（按照copy文件的逻辑）
     */
    fun calVolume() {
        if (isUpdatingFields) return // 程序填充数据时不触发计算

        var length = 0F
        var width = 0F
        var height = 0F

        // 根据当前模式获取对应的长宽高值
        if (isSmall) {
            if (et_small_packet_length.text.isNotEmpty()) {
                length = et_small_packet_length.text.toString().toFloat()
            }
            if (et_small_packet_width.text.isNotEmpty()) {
                width = et_small_packet_width.text.toString().toFloat()
            }
            if (et_small_packet_height.text.isNotEmpty()) {
                height = et_small_packet_height.text.toString().toFloat()
            }
        } else {
            if (et_large_packet_length.text.isNotEmpty()) {
                length = et_large_packet_length.text.toString().toFloat()
            }
            if (et_large_packet_width.text.isNotEmpty()) {
                width = et_large_packet_width.text.toString().toFloat()
            }
            if (et_large_packet_height.text.isNotEmpty()) {
                height = et_large_packet_height.text.toString().toFloat()
            }
        }

        // 计算体积
        val volume = length * width * height

        // 更新对应的体积显示（按照copy文件逻辑）
        isUpdatingFields = true
        if (isSmall) {
            et_small_packet_volume.setText(volume.toInt().toString())
            smallProductPackingVO?.packingVolume = volume
            // 同时计算大包装体积：按照copy文件逻辑使用largeProductPackingVO?.packingNumber
            try {
                largeProductPackingVO?.packingVolume = (smallProductPackingVO?.packingVolume!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
            } catch (e: Exception) {
                // 忽略计算错误
            }
        } else {
            et_large_packet_volume.setText(volume.toInt().toString())
            largeProductPackingVO?.packingVolume = volume
            // 同时计算小包装体积：按照copy文件逻辑使用largeProductPackingVO?.packingNumber
            try {
                smallProductPackingVO?.packingVolume = (largeProductPackingVO?.packingVolume!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
            } catch (e: Exception) {
                // 忽略计算错误
            }
        }
        isUpdatingFields = false
    }

    /**
     * 获取商品信息数据
     */
    private fun getData() {
        mPresenter.queryProductInfoMaintain(ProductInfoMaintainQueryPost(productCode))
    }

    /**
     * 显示存储属性选择对话框
     */
    private fun showStorageAttributeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            storageAttributeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<StorageAttributeList>> { _, t ->
                et_storage_properties.setText(t.key)
                storageAttribute = t.value
            },
            null
        )
    }

    /**
     * 显示零货库ABC分类选择对话框
     */
    private fun showABCTypeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            abcTypeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<AbcTypeList>> { _, t ->
                et_product_classification.setText(t.key)
                lhkLogicalregionRelation?.goodspositionAbcType = t.value.dictCode

                // 请求库存上下限
                mPresenter.getUpperLowerProductInfoMaintain(
                    GetUpperLowerProductInfoMaintainPost(
                        productCode,
                        productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode
                    ), false
                )

                // 请求逻辑区域
                if (productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode != null) {
                    val logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(
                        productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode,
                        productInfoMaintainQueryBean!!.productCode
                    )
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, false)
                } else {
                    showToast("当前商品没有逻辑区域")
                }
            },
            null
        )
    }

    /**
     * 显示整件库ABC分类选择对话框
     */
    private fun showWholeABCTypeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            wholeAbcTypeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<AbcTypeList>> { _, t ->
                et_whole_product_classification.setText(t.key)
                zjkLogicalregionRelation?.goodspositionAbcType = t.value.dictCode

                // 请求库存上下限
                mPresenter.getUpperLowerProductInfoMaintain(
                    GetUpperLowerProductInfoMaintainPost(
                        productCode,
                        productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode
                    ), true
                )

                // 请求逻辑区域
                if (productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode != null) {
                    val logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(
                        productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode,
                        productInfoMaintainQueryBean!!.productCode
                    )
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, true)
                }
            },
            null
        )
    }

    /**
     * 提交数据
     */
    private fun submitData() {
        // 数据验证
        if (storageAttribute == null) {
            showToast("请选择存储属性")
            return
        }

        if (ifErrorProne == null) {
            showToast("请选择是否易收错")
            return
        }

        var storageAttributeCode = storageAttribute?.code ?: ""

        // 收集小包装数据
        collectSmallPackageData()
        // 收集大包装数据
        collectLargePackageData()

        mPresenter.saveProductInfoMaintain(
            SaveProductInfoMaintainPost(
                productInfoMaintainQueryBean?.id!!,
                productCode,
                storageAttributeCode,
                ifErrorProne!!,
                smallProductPackingVO!!,
                largeProductPackingVO!!,
                lhkLogicalregionRelation!!,
                zjkLogicalregionRelation!!
            )
        )
    }

    /**
     * 收集小包装数据
     */
    private fun collectSmallPackageData() {
        smallProductPackingVO?.apply {
            packingLong = if (et_small_packet_length.text.toString().isNotEmpty())
                et_small_packet_length.text.toString().toFloat() else 0F
          packingWide = if (et_small_packet_width.text.toString().isNotEmpty())
                et_small_packet_width.text.toString().toFloat() else 0F
          packingHigh = if (et_small_packet_height.text.toString().isNotEmpty())
                et_small_packet_height.text.toString().toFloat() else 0F
            packingVolume = if (et_small_packet_volume.text.toString().isNotEmpty())
                et_small_packet_volume.text.toString().toFloat() else 0F
            packingWeight = if (et_small_packet_weight.text.toString().isNotEmpty())
                et_small_packet_weight.text.toString().toFloat() else 0F
        }
    }

    /**
     * 收集大包装数据
     */
    private fun collectLargePackageData() {
        largeProductPackingVO?.apply {
            packingLong = if (et_large_packet_length.text.toString().isNotEmpty())
                et_large_packet_length.text.toString().toFloat() else 0F
          packingWide = if (et_large_packet_width.text.toString().isNotEmpty())
                et_large_packet_width.text.toString().toFloat() else 0F
          packingHigh = if (et_large_packet_height.text.toString().isNotEmpty())
                et_large_packet_height.text.toString().toFloat() else 0F
            packingVolume = if (et_large_packet_volume.text.toString().isNotEmpty())
                et_large_packet_volume.text.toString().toFloat() else 0F
            packingWeight = if (et_large_packet_weight.text.toString().isNotEmpty())
                et_large_packet_weight.text.toString().toFloat() else 0F
        }
    }

    /**
     * 查询商品信息维护成功回调
     */
    override fun queryProductInfoMaintainSuccess(result: BaseResponseBean<ProductInfoMaintainQueryBean>) {
        if (result.code == 0) {
            productInfoMaintainQueryBean = result.result

            smallProductPackingVO = productInfoMaintainQueryBean?.smallProductPackingVO
            largeProductPackingVO = productInfoMaintainQueryBean?.largeProductPackingVO
            lhkLogicalregionRelation = productInfoMaintainQueryBean?.lhkLogicalregionRelation
            zjkLogicalregionRelation = productInfoMaintainQueryBean?.zjkLogicalregionRelation

            // 初始化下拉数据
            productInfoMaintainQueryBean?.storageAttributeList?.forEach {
                storageAttributeList.add(PickerDialogManager.SelectItemModel(it.name, it))
            }

            productInfoMaintainQueryBean?.abcTypeList?.forEach {
                abcTypeList.add(PickerDialogManager.SelectItemModel(it.dictName, it))
                wholeAbcTypeList.add(PickerDialogManager.SelectItemModel(it.dictName, it))
            }

            // 填充基本信息
            fillBasicInfo()
            // 填充包装信息
            fillPackageInfo()
            // 填充逻辑区域信息
            fillLogicalAreaInfo()
        }
    }

    /**
     * 填充基本信息
     */
    private fun fillBasicInfo() {
        tv_product_name.text = productInfoMaintainQueryBean?.productName
        tv_product_number.text = productInfoMaintainQueryBean?.productCode
        tv_dose.text = productInfoMaintainQueryBean?.dosageForm
        // 易收错设置
        if (productInfoMaintainQueryBean?.ifErrorProne == 0) {
            rb_is_receive_no.isChecked = true
            rb_is_receive_yes.isChecked = false
            ifErrorProne = "0"
        } else {
            rb_is_receive_no.isChecked = false
            rb_is_receive_yes.isChecked = true
            ifErrorProne = "1"
        }

        // 存储属性设置
        productInfoMaintainQueryBean?.storageAttributeList?.forEach {
            if (it.code == productInfoMaintainQueryBean?.storageAttribute) {
                et_storage_properties.setText(it.name)
                storageAttribute = it
            }
        }
    }

    /**
     * 填充包装信息
     */
    private fun fillPackageInfo() {
        isUpdatingFields = true // 设置标志位，防止触发联动计算

        // 小包装信息
        smallProductPackingVO?.let { small ->
            if (small.packingLong != 0F) et_small_packet_length.setText(small.packingLong.toString())
            if (small.packingWide != 0F) et_small_packet_width.setText(small.packingWide.toString())
            if (small.packingHigh != 0F) et_small_packet_height.setText(small.packingHigh.toString())
            if (small.packingVolume != 0F) et_small_packet_volume.setText(small.packingVolume.toString())
            if (small.packingWeight != 0F) et_small_packet_weight.setText(small.packingWeight.toString())
        }

        // 大包装信息
        largeProductPackingVO?.let { large ->
            if (large.packingLong != 0F) et_large_packet_length.setText(large.packingLong.toString())
            if (large.packingWide != 0F) et_large_packet_width.setText(large.packingWide.toString())
            if (large.packingHigh != 0F) et_large_packet_height.setText(large.packingHigh.toString())
            if (large.packingVolume != 0F) et_large_packet_volume.setText(large.packingVolume.toString())
            if (large.packingWeight != 0F) et_large_packet_weight.setText(large.packingWeight.toString())
        }

        isUpdatingFields = false // 重置标志位
    }

    /**
     * 填充逻辑区域信息
     */
    private fun fillLogicalAreaInfo() {
        // 零货库信息
        tv_storage_room.text = lhkLogicalregionRelation?.storageRoomName
        et_logical_area.setText(lhkLogicalregionRelation?.logicalRegionName)
        et_inventory_limit.setText(lhkLogicalregionRelation?.stockUpper)
        et_inventory_lower_limit.setText(lhkLogicalregionRelation?.stockLower)

        // 整件库信息
        tv_whole_storage_room.text = zjkLogicalregionRelation?.storageRoomName
        et_whole_logical_area.setText(zjkLogicalregionRelation?.logicalRegionName)
        et_whole_inventory_limit.setText(zjkLogicalregionRelation?.stockUpper)
        et_whole_inventory_lower_limit.setText(zjkLogicalregionRelation?.stockLower)

        // ABC分类设置
        if (lhkLogicalregionRelation?.goodspositionAbcType != null) {
            et_product_classification.setText(lhkLogicalregionRelation?.goodspositionAbcTypeName)
            // 请求逻辑区域数据
            requestLogicalRegionData(false)
        }

        if (zjkLogicalregionRelation?.goodspositionAbcType != null) {
            et_whole_product_classification.setText(zjkLogicalregionRelation?.goodspositionAbcTypeName)
            // 请求逻辑区域数据
            requestLogicalRegionData(true)
        }
    }

    /**
     * 请求逻辑区域数据
     */
    private fun requestLogicalRegionData(isWhole: Boolean) {
        if (isWhole) {
            zjkLogicalregionRelation?.let { relation ->
                if (!relation.storageRoomCode.isNullOrEmpty() && !relation.goodspositionAbcType.isNullOrEmpty()) {
                    val post = LogicalRegionProductInfoMaintainPost(
                        relation.storageRoomCode,
                        relation.goodspositionAbcType,
                        productInfoMaintainQueryBean!!.productCode
                    )
                    if (!relation.logicalRegionName.isNullOrEmpty()) {
                        post.logicalRegionName = relation.logicalRegionName
                    }
                    mPresenter.logicalRegionProductInfoMaintain(post, isWhole)
                }
            }
        } else {
            lhkLogicalregionRelation?.let { relation ->
                if (!relation.storageRoomCode.isNullOrEmpty() && !relation.goodspositionAbcType.isNullOrEmpty()) {
                    val post = LogicalRegionProductInfoMaintainPost(
                        relation.storageRoomCode,
                        relation.goodspositionAbcType,
                        productInfoMaintainQueryBean!!.productCode
                    )
                    if (!relation.logicalRegionName.isNullOrEmpty()) {
                        post.logicalRegionName = relation.logicalRegionName
                    }
                    mPresenter.logicalRegionProductInfoMaintain(post, isWhole)
                }
            }
        }
    }

    /**
     * 保存商品信息维护成功回调
     */
    override fun saveProductInfoMaintainSuccess(result: String) {
      showToast("保存成功")
      finish()
    }

  /**
   * 初始化零货库逻辑区域对话框
   */
  private fun initCommodityMaintenanceDialog() {
    dialog_commodity_maintenance = Dialog(this, R.style.NoTitleDialog)
    val view = LayoutInflater.from(this).inflate(R.layout.dialog_commodity_maintenance, null)
    dialog_commodity_maintenance.setContentView(view)

    val window = dialog_commodity_maintenance.window
    val windowparams = window?.attributes
    // 手机端适配：使用屏幕宽度的90%，高度自适应
    windowparams?.width = (resources.displayMetrics.widthPixels * 0.9).toInt()
    windowparams?.height = WindowManager.LayoutParams.WRAP_CONTENT
    windowparams?.gravity = Gravity.CENTER
    window?.attributes = windowparams

    val rv_commodity: RecyclerView = view.findViewById(R.id.rv_commodity)
    commodityMaintenanceAdapter = CommodityMaintenanceAdapter()
    rv_commodity.adapter = commodityMaintenanceAdapter
    // 手机端使用2列网格布局
    rv_commodity.layoutManager = GridLayoutManager(this, 2)

    commodityMaintenanceAdapter?.setOnItemClickListener { _, _, position ->
      selectCurrentItem(position, 2)
    }

    //关闭按钮
    val tvClose: TextView = view.findViewById(R.id.tv_close)
    tvClose.setOnClickListener { dialog_commodity_maintenance.hide() }
  }

  /**
   * 初始化整件库逻辑区域对话框
   */
  private fun initWholeCommodityMaintenanceDialog() {
    dialog_whole_commodity_maintenance = Dialog(this, R.style.NoTitleDialog)
    val view = LayoutInflater.from(this).inflate(R.layout.dialog_commodity_maintenance, null)
    dialog_whole_commodity_maintenance.setContentView(view)

    val window = dialog_whole_commodity_maintenance.window
    val windowparams = window?.attributes
    // 手机端适配：使用屏幕宽度的90%，高度自适应
    windowparams?.width = (resources.displayMetrics.widthPixels * 0.9).toInt()
    windowparams?.height = WindowManager.LayoutParams.WRAP_CONTENT
    windowparams?.gravity = Gravity.CENTER
    window?.attributes = windowparams

    val rv_commodity: RecyclerView = view.findViewById(R.id.rv_commodity)
    wholeCommodityMaintenanceAdapter = CommodityMaintenanceAdapter()
    rv_commodity.adapter = wholeCommodityMaintenanceAdapter
    // 手机端使用2列网格布局
    rv_commodity.layoutManager = GridLayoutManager(this, 2)

    wholeCommodityMaintenanceAdapter?.setOnItemClickListener { _, _, position ->
      selectWholeCurrentItem(position, 2)
    }

    //关闭按钮
    val tvClose: TextView = view.findViewById(R.id.tv_close)
    tvClose.setOnClickListener { dialog_whole_commodity_maintenance.hide() }
  }

    /**
     * 选择零货库逻辑区域
     * @param position 选中项位置
     * @param scene 场景：1=默认选中，2=点击选择
     */
    private fun selectCurrentItem(position: Int, scene: Int) {
        if (commodityMaintenanceList.size > position) {
            val item: LogicalRegionProductInfoMaintainBean = commodityMaintenanceList[position]

            // 只有点击选择时才切换状态
            if (scene == 2) {
                item.isSelected = !item.isSelected
            }

            commodityMaintenanceList[position] = item
            commodityMaintenanceAdapter?.notifyItemRangeChanged(position, 1)

            // 收集所有选中的项目
            var nameList = StringBuffer()
            var codeList = StringBuffer()
            commodityMaintenanceList.forEach {
                if (it.isSelected) {
                    codeList.append("${it.dictCode},")
                    nameList.append("${it.dictName},")
                }
            }

            // 更新UI和数据
            et_logical_area.setText(nameList.toString())
            lhkLogicalregionRelation?.logicalRegionCode = codeList.toString()
            lhkLogicalregionRelation?.logicalRegionName = nameList.toString()

            // 点击选择时不关闭对话框，允许多选
            // dialog_commodity_maintenance.hide() // 移除这行
        }
    }

    /**
     * 选择整件库逻辑区域
     * @param position 选中项位置
     * @param scene 场景：1=默认选中，2=点击选择
     */
    private fun selectWholeCurrentItem(position: Int, scene: Int) {
        if (wholeCommodityMaintenanceList.size > position) {
            val item: LogicalRegionProductInfoMaintainBean = wholeCommodityMaintenanceList[position]

            // 只有点击选择时才切换状态
            if (scene == 2) {
                item.isSelected = !item.isSelected
            }

            wholeCommodityMaintenanceList[position] = item
            wholeCommodityMaintenanceAdapter?.notifyItemRangeChanged(position, 1)

            // 收集所有选中的项目
            var nameList = StringBuffer()
            var codeList = StringBuffer()
            wholeCommodityMaintenanceList.forEach {
                if (it.isSelected) {
                    codeList.append("${it.dictCode},")
                    nameList.append("${it.dictName},")
                }
            }

            // 更新UI和数据
            et_whole_logical_area.setText(nameList.toString())
            zjkLogicalregionRelation?.logicalRegionCode = codeList.toString()
            zjkLogicalregionRelation?.logicalRegionName = nameList.toString()

            // 点击选择时不关闭对话框，允许多选
            // dialog_whole_commodity_maintenance.hide() // 移除这行
        }
    }
    /**
     * 请求库存上下限回调
     */
    override fun getUpperLowerProductInfoMaintainSuccess(
      getUpperLowerProductInfoMaintainBean: GetUpperLowerProductInfoMaintainBean,
      isWhole: Boolean
    ) {
      if (isWhole) {
        et_whole_inventory_limit.setText(getUpperLowerProductInfoMaintainBean.stockUpper)
        et_whole_inventory_lower_limit.setText(getUpperLowerProductInfoMaintainBean.stockLower)
        zjkLogicalregionRelation?.stockUpper = getUpperLowerProductInfoMaintainBean.stockUpper
        zjkLogicalregionRelation?.stockLower = getUpperLowerProductInfoMaintainBean.stockLower
      } else {
        et_inventory_limit.setText(getUpperLowerProductInfoMaintainBean.stockUpper)
        et_inventory_lower_limit.setText(getUpperLowerProductInfoMaintainBean.stockLower)
        lhkLogicalregionRelation?.stockUpper = getUpperLowerProductInfoMaintainBean.stockUpper
        lhkLogicalregionRelation?.stockLower = getUpperLowerProductInfoMaintainBean.stockLower
      }
    }

    /**
     * 请求逻辑区域成功回调 (完整版本)
     */
    override fun logicalRegionProductInfoMaintainSuccess(
        logicalRegionProductInfoMaintainBean: java.util.ArrayList<LogicalRegionProductInfoMaintainBean>,
        isWhole: Boolean
    ) {
        if (isWhole) {
            et_whole_logical_area.setText("")
            wholeCommodityMaintenanceList = logicalRegionProductInfoMaintainBean

            // 处理默认选中逻辑
            var logicalRegionCodelist: MutableList<String> = mutableListOf()
            if (productInfoMaintainQueryBean?.zjkLogicalregionRelation != null) {
                logicalRegionCodelist = (productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionCode?.split(",") as MutableList<String>?)!!
            }

            if (logicalRegionCodelist.size > 0) {
                for ((index, item) in wholeCommodityMaintenanceList.withIndex()) {
                    for (itemz in logicalRegionCodelist) {
                        if (item.dictName == itemz) {
                            wholeCommodityMaintenanceList[index].isSelected = true
                            selectWholeCurrentItem(index, 1)
                        }
                    }
                }
            }
            wholeCommodityMaintenanceAdapter?.setNewData(wholeCommodityMaintenanceList)
        } else {
            et_logical_area.setText("")
            commodityMaintenanceList = logicalRegionProductInfoMaintainBean

            // 处理默认选中逻辑
            var logicalRegionCodelist: MutableList<String> = mutableListOf()
            if (productInfoMaintainQueryBean?.lhkLogicalregionRelation != null) {
                logicalRegionCodelist = (productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionCode?.split(",") as MutableList<String>?)!!
            }

            if (logicalRegionCodelist.size > 0) {
                for ((index, item) in commodityMaintenanceList.withIndex()) {
                    for (itemz in logicalRegionCodelist) {
                        if (item.dictName == itemz) {
                            commodityMaintenanceList[index].isSelected = true
                            selectCurrentItem(index, 1)
                        }
                    }
                }
            }
            commodityMaintenanceAdapter?.setNewData(commodityMaintenanceList)
        }
    }

    /**
     * Activity销毁时的处理
     */
    override fun onDestroy() {
        super.onDestroy()
        if (::dialog_commodity_maintenance.isInitialized) {
            dialog_commodity_maintenance.dismiss()
        }
        if (::dialog_whole_commodity_maintenance.isInitialized) {
            dialog_whole_commodity_maintenance.dismiss()
        }
    }
}































