package com.xyy.wms.pda.model.instorage.checkaccept

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.wms.pda.api.ApiInAcceptServiceNew
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.bean.instorage.InitCheckPageBean
import com.xyy.wms.pda.bean.instorage.PackageBarCodeBean
import com.xyy.wms.pda.bean.instorage.ProductCodePost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDoSubmitPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownPost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPagePost
import com.xyy.wms.pda.bean.instorage.SecondLoginPost
import com.xyy.wms.pda.bean.instorage.SecondLoginResult
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderSavePost
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerBean
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerPost
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierBean
import com.xyy.wms.pda.bean.instorage.checkaccept.WaitCheckInfoBySupplierPost
import com.xyy.wms.pda.bean.instorage.PictureUploadResponse
import com.xyy.wms.pda.bean.instorage.PictureDeleteRequest
import com.xyy.wms.pda.bean.instorage.GoodsPicturesResult
import com.xyy.wms.pda.bean.instorage.UploadPicturePost
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillDetailContractNew
import com.xyy.wms.pda.net.RetrofitCreateHelper
import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.RequestBody

/**
 * 入库验收单-详情
 */
class CheckAcceptBillDetailModelNew : BaseModel(), CheckAcceptBillDetailContractNew.ICheckAcceptBillModelNew {
    companion object {
        fun newInstance(): CheckAcceptBillDetailModelNew = CheckAcceptBillDetailModelNew()
    }
    override fun getDictListByNameAndType(dictName: String, dictType: String): Observable<BaseResponseBean<List<DictListByNameAndType>>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).getDictListByNameAndType(dictName, dictType)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun selectProductCode(productCode: ProductCodePost): Observable<BaseResponseBean<PackageBarCodeBean>> {
      return RetrofitCreateHelper
        .createApi(ApiInAcceptServiceNew::class.java)
        .selectProductCode(productCode)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun initCheckPage(initCheckPagePost: InitCheckPagePost): Observable<BaseResponseBean<InitCheckPageBean>> {
      return RetrofitCreateHelper
        .createApi(ApiInAcceptServiceNew::class.java)
        .initCheckPage(initCheckPagePost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun turnDownCheckOrder(checkOrderTurnDownPost: CheckOrderTurnDownPost): Observable<BaseResponseBean<CheckOrderTurnDownBean>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).turnDownCheckOrder(checkOrderTurnDownPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun doSubmitCheckOrder(checkOrderDoSubmitPost: CheckOrderDoSubmitPost): Observable<BaseResponseBean<String>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).doSubmitCheckOrder(checkOrderDoSubmitPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun secondLogin(secondLoginPost: SecondLoginPost): Observable<BaseResponseBean<SecondLoginResult>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).secondLogin(secondLoginPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun selectContainer(selectContainerPost: SelectContainerPost): Observable<BaseResponseBean<SelectContainerBean>> {
      return RetrofitCreateHelper
        .createApi(ApiInAcceptServiceNew::class.java)
        .selectContainer(selectContainerPost)
        .compose(RxHelper.rxSchedulerHelper())
    }
    override fun saveCheckOrder(checkOrderSavePost: CheckOrderSavePost): Observable<BaseResponseBean<String>> {
      return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java).saveCheckOrder(checkOrderSavePost)
        .compose(RxHelper.rxSchedulerHelper())
    }

    // 异常图片上传相关方法实现
    override fun uploadPicture(files: UploadPicturePost): Observable<BaseResponseBean<List<String>>> {
        android.util.Log.d("ExceptionPictureDialog", "CheckAcceptModel.uploadPicture() Model方法被调用")
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .uploadPicture(files)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun deleteExceptionPictures(request: PictureDeleteRequest): Observable<BaseResponseBean<Boolean>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .deleteExceptionPictures(request)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun getExceptionPictures(businessId: String): Observable<BaseResponseBean<List<String>>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .getExceptionPictures(businessId)
            .compose(RxHelper.rxSchedulerHelper())
    }

    override fun showGoosPicture(barcode: Map<String,String>): Observable<BaseResponseBean<List<GoodsPicturesResult>>> {
        return RetrofitCreateHelper.createApi(ApiInAcceptServiceNew::class.java)
            .showGoosPicture(barcode)
            .compose(RxHelper.rxSchedulerHelper())
    }
}

