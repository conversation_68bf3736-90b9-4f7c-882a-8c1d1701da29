package com.xyy.wms.pad.instorage.activity.newInspection

import android.app.Dialog
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.widget.Button
import android.widget.EditText
import android.widget.RadioButton
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.scwang.smartrefresh.layout.util.DensityUtil
import com.xyy.wms.pad.common.base.BasePresenter
import com.xyy.wms.pad.common.base.activity.BaseMVPCompatActivity
import com.xyy.wms.pad.common.data.protocol.BaseResponseBean
import com.xyy.wms.pad.common.utils.CommonInfoUtils
import com.xyy.wms.pad.common.utils.PickerDialogManager
import com.xyy.wms.pad.instorage.R
import com.xyy.wms.pad.instorage.adapter.CommodityMaintenanceAdapter
import com.xyy.wms.pad.instorage.bean.*
import com.xyy.wms.pad.instorage.bean.cagecar.*
import com.xyy.wms.pad.instorage.contract.CommodityInformationMaintenanceContract
import com.xyy.wms.pad.instorage.presenter.newinspection.CommodityInformationMaintenancePresenter
import kotlinx.android.synthetic.main.activity_commodity_information_maintenance.*
import me.yokeyword.fragmentation.SupportActivity
import java.math.BigDecimal
import java.util.ArrayList

/**
 * <AUTHOR>
 * @Description 商品信息维护
 * @Date 2022/3/29
 */
class CommodityInformationMaintenanceActivity :
    BaseMVPCompatActivity<CommodityInformationMaintenancePresenter>(),
    CommodityInformationMaintenanceContract.CommodityInformationMaintenanceView {

    private lateinit var mTitle: TextView
    private lateinit var mPersonalInfo: TextView
    private lateinit var mSubmit: Button
    private lateinit var mBack: Button

    private lateinit var tv_product_name: TextView                       // 商品名称
    private lateinit var tv_product_number: TextView                     // 商品编号
    private lateinit var tv_product_class: TextView                      // 商品大类
    private lateinit var tv_dose: TextView                               // 剂量
    private lateinit var tv_storage_conditions: TextView                 // 存储条件
    private lateinit var tv_product_piece_packing: TextView              // 件包装规格
    private lateinit var tv_specifications: TextView                     // 规格
    private lateinit var rb_small_package: RadioButton                  // 小包装按钮
    private lateinit var rb_big_package: RadioButton                    // 大包装按钮
    private lateinit var tv_packet_length_tag: TextView                 // tag
    private lateinit var tv_packet_width_tag: TextView                  // tag
    private lateinit var tv_packet_height_tag: TextView                 // tag
    private lateinit var tv_packet_volume_tag: TextView                 // tag
    private lateinit var tv_packet_weight_tag: TextView                 // tag
    private lateinit var et_packet_length: EditText                     // 包装长
    private lateinit var et_packet_width: EditText                      // 包装宽
    private lateinit var et_packet_height: EditText                     // 包装高
    private lateinit var et_packet_weight: EditText                     // 包装高
    private lateinit var et_packet_volume: EditText                     // 包装体积
    private lateinit var rb_is_receive_yes: RadioButton                 // 是否易收错
    private lateinit var rb_is_receive_no: RadioButton                  // 是否易收错
    private lateinit var et_storage_properties: EditText                // 存放属性

    private lateinit var et_logical_area: EditText
    private lateinit var et_whole_logical_area: EditText
    private lateinit var et_product_classification: EditText
    private lateinit var et_whole_product_classification: EditText

    private lateinit var et_inventory_limit: TextView
    private lateinit var et_inventory_lower_limit: TextView
    private lateinit var et_whole_inventory_limit: TextView
    private lateinit var et_whole_inventory_lower_limit: TextView

    private lateinit var tv_storage_room: TextView
    private lateinit var tv_whole_storage_room: TextView

    private var isSmall: Boolean = false
    private lateinit var productCode: String
    private var storageAttribute: StorageAttributeList? = null// 存放属性
    private var ifErrorProne: String? = null     // 是否易收错
    private var smallProductPackingVO: SmallProductPackingVO? = null
    private var largeProductPackingVO: LargeProductPackingVO? = null
    private var lhkLogicalregionRelation: LhkLogicalregionRelation? = null
    private var zjkLogicalregionRelation: ZjkLogicalregionRelation? = null

    private var storageAttributeList: MutableList<PickerDialogManager.SelectItemModel<StorageAttributeList>> =
        ArrayList()
    private var abcTypeList: MutableList<PickerDialogManager.SelectItemModel<AbcTypeList>> =
        ArrayList()
    private var wholeAbcTypeList: MutableList<PickerDialogManager.SelectItemModel<AbcTypeList>> =
        ArrayList()

    private var productInfoMaintainQueryBean: ProductInfoMaintainQueryBean? = null

    //逻辑区域适配器
    private lateinit var dialog_commodity_maintenance: Dialog
    private lateinit var dialog_whole_commodity_maintenance: Dialog
    private var commodityMaintenanceAdapter: CommodityMaintenanceAdapter? = null
    private var wholeCommodityMaintenanceAdapter: CommodityMaintenanceAdapter? = null

    var commodityMaintenanceList: ArrayList<LogicalRegionProductInfoMaintainBean> = arrayListOf()
    var wholeCommodityMaintenanceList: ArrayList<LogicalRegionProductInfoMaintainBean> =
        arrayListOf()

    override fun initTransferData() {
        super.initTransferData()
        intent?.let {
            productCode = it.getStringExtra("productCode")
        }
    }

    override fun getLayoutId(): Int = R.layout.activity_commodity_information_maintenance

    override fun initPresenter(): BasePresenter<*, *> = CommodityInformationMaintenancePresenter()

    override fun initView(savedInstanceState: Bundle?) {

        findViewById()

        CommonInfoUtils.setTitle(mPersonalInfo, "验收员", mTitle, "商品信息维护")

        addListener()

        getData()
    }

    private fun findViewById() {
        mPersonalInfo = findViewById(R.id.tv_picking_member)
        mTitle = findViewById(R.id.tv_title)
        mSubmit = findViewById(R.id.btn_submit)
        mBack = findViewById(R.id.btn_back)

        tv_product_name = findViewById(R.id.tv_product_name)
        tv_product_number = findViewById(R.id.tv_product_number)
        tv_product_class = findViewById(R.id.tv_product_class)
        tv_dose = findViewById(R.id.tv_dose)
        tv_storage_conditions = findViewById(R.id.tv_storage_conditions)
        tv_product_piece_packing = findViewById(R.id.tv_product_piece_packing)
        tv_specifications = findViewById(R.id.tv_specifications)
        rb_small_package = findViewById(R.id.rb_small_package)
        rb_big_package = findViewById(R.id.rb_big_package)
        tv_packet_length_tag = findViewById(R.id.tv_packet_length_tag)
        tv_packet_width_tag = findViewById(R.id.tv_packet_width_tag)
        tv_packet_height_tag = findViewById(R.id.tv_packet_height_tag)
        tv_packet_volume_tag = findViewById(R.id.tv_packet_volume_tag)
        tv_packet_weight_tag = findViewById(R.id.tv_packet_weight_tag)
        et_packet_length = findViewById(R.id.et_packet_length)
        et_packet_width = findViewById(R.id.et_packet_width)
        et_packet_height = findViewById(R.id.et_packet_height)
        et_packet_weight = findViewById(R.id.et_packet_weight)
        et_packet_volume = findViewById(R.id.et_packet_volume)
        rb_is_receive_yes = findViewById(R.id.rb_is_receive_yes)
        rb_is_receive_no = findViewById(R.id.rb_is_receive_no)
        et_storage_properties = findViewById(R.id.et_storage_properties)
        et_logical_area = findViewById(R.id.et_logical_area)
        et_whole_logical_area = findViewById(R.id.et_whole_logical_area)
        et_product_classification = findViewById(R.id.et_product_classification)
        et_whole_product_classification = findViewById(R.id.et_whole_product_classification)
        et_inventory_limit = findViewById(R.id.et_inventory_limit)
        et_inventory_lower_limit = findViewById(R.id.et_inventory_lower_limit)
        et_whole_inventory_limit = findViewById(R.id.et_whole_inventory_limit)
        et_whole_inventory_lower_limit = findViewById(R.id.et_whole_inventory_lower_limit)
        tv_storage_room = findViewById(R.id.tv_storage_room)
        tv_whole_storage_room = findViewById(R.id.tv_whole_storage_room)

        initCommodityMaintenanceDialog()
        initWholeCommodityMaintenanceDialog()
    }

    private fun addListener() {
        mBack.setOnClickListener {
            val intent = Intent()
            this.setResult(RESULT_OK, intent)
            this.finish()
            finish()
        }
        et_logical_area.setOnClickListener {
            if (commodityMaintenanceList.size <= 0) {
                showToast("当前没有逻辑区域")
                return@setOnClickListener
            }
            if (dialog_commodity_maintenance != null) {
                dialog_commodity_maintenance.show()
            }
        }
        et_whole_logical_area.setOnClickListener {
            if (wholeCommodityMaintenanceList.size <= 0) {
                showToast("当前没有逻辑区域")
                return@setOnClickListener
            }
            if (dialog_whole_commodity_maintenance != null) {
                dialog_whole_commodity_maintenance.show()
            }
        }
        rb_small_package.setOnClickListener { switchProductPack(true) }
        rb_big_package.setOnClickListener { switchProductPack(false) }
        et_storage_properties.setOnClickListener { showStorageAttributeListDialog() }
        et_product_classification.setOnClickListener { showABCTypeListDialog() }
        et_whole_product_classification.setOnClickListener { showWholeABCTypeListDialog() }
        mSubmit.setOnClickListener { submitData() }
        rb_is_receive_yes.setOnClickListener { this.ifErrorProne = "1" }
        rb_is_receive_no.setOnClickListener { this.ifErrorProne = "0" }

        et_packet_length.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == ""){
                    return
                }
                calVolume()
                if (isSmall) {
                    smallProductPackingVO?.packingLong = p0.toString().toFloat()
                } else {
                    largeProductPackingVO?.packingLong = p0.toString().toFloat()
                }
            }
        })

        et_packet_width.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == ""){
                    return
                }
                calVolume()
                if (isSmall) {
                    smallProductPackingVO?.packingWide = p0.toString().toFloat()
                } else {
                    largeProductPackingVO?.packingWide = p0.toString().toFloat()
                }
            }
        })

        et_packet_height.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == ""){
                    return
                }
                calVolume()
                if (isSmall) {
                    smallProductPackingVO?.packingHigh = p0.toString().toFloat()
                } else {
                    largeProductPackingVO?.packingHigh = p0.toString().toFloat()
                }
            }
        })

        et_packet_volume.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "") {
                    return
                }
                if (isSmall) {
                    smallProductPackingVO?.packingVolume = p0.toString().toFloat()
                    largeProductPackingVO?.packingVolume = (smallProductPackingVO?.packingVolume!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
                } else {
                    largeProductPackingVO?.packingVolume = p0.toString().toFloat()
                    smallProductPackingVO?.packingVolume = (largeProductPackingVO?.packingVolume!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
                }
            }
        })

        et_packet_width.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "") {
                    return
                }
                if (isSmall) {
                    smallProductPackingVO?.packingWide = p0.toString().toFloat()
                } else {
                    largeProductPackingVO?.packingWide = p0.toString().toFloat()
                }
            }
        })

        et_packet_weight.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                if (p0.toString() == "") {
                    return
                }
                if (isSmall) {
                    smallProductPackingVO?.packingWeight = p0.toString().toFloat()
                    largeProductPackingVO?.packingWeight = (smallProductPackingVO?.packingWeight!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
                } else {
                    largeProductPackingVO?.packingWeight = p0.toString().toFloat()
                    smallProductPackingVO?.packingWeight = (largeProductPackingVO?.packingWeight!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
                }
            }
        })

        et_inventory_limit.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                lhkLogicalregionRelation?.stockUpper = p0.toString()
            }
        })

        et_inventory_lower_limit.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                lhkLogicalregionRelation?.stockLower = p0.toString()
            }
        })

        et_whole_inventory_limit.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                zjkLogicalregionRelation?.stockUpper = p0.toString()
            }
        })

        et_whole_inventory_lower_limit.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                zjkLogicalregionRelation?.stockLower = p0.toString()
            }
        })
    }

    private fun getData() {
        mPresenter.queryProductInfoMaintain(ProductInfoMaintainQueryPost(productCode))
    }

    /**
     * 显示存放属性
     */
    private fun showStorageAttributeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            this.storageAttributeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<StorageAttributeList>> { _, t ->
                et_storage_properties.setText(t.key)
                storageAttribute = t.value
            },
            null
        )
    }

    private fun showABCTypeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            this.abcTypeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<AbcTypeList>> { _, t ->
                et_product_classification.setText(t.key)
                this.lhkLogicalregionRelation?.goodspositionAbcType = t.value.dictCode
                // 请求库存上下限
                mPresenter.getUpperLowerProductInfoMaintain(
                    GetUpperLowerProductInfoMaintainPost(
                        productCode,
                        this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode
                    ), false
                )
                // 请求逻辑区域
                if (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode != null) {
                    var logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode!!, t.value.dictCode, this.productInfoMaintainQueryBean!!.productCode)
                    // if (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionName != null) { lhkLogicalregionRelation?.logicalRegionName = this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionName!! }
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, false)
                } else {
                    showToast("当前商品没有逻辑区域")
                }
            },
            null
        )
    }

    private fun showWholeABCTypeListDialog() {
        PickerDialogManager.showSingleSelectPicker(
            mContext,
            this.wholeAbcTypeList,
            PickerDialogManager.OnOptionsSelectCallback2<PickerDialogManager.SelectItemModel<AbcTypeList>> { _, t ->
                et_whole_product_classification.setText(t.key)
                this.zjkLogicalregionRelation?.goodspositionAbcType = t.value.dictCode
                // 请求库存上下限
                mPresenter.getUpperLowerProductInfoMaintain(
                    GetUpperLowerProductInfoMaintainPost(
                        productCode,
                        this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode!!,
                        t.value.dictCode
                    ), true
                )
                // 请求逻辑区域
                if (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode != null) {
                    var logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode!!, t.value.dictCode, this.productInfoMaintainQueryBean!!.productCode)
                    // if (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionName != null) { logicalRegionProductInfoMaintainPost.logicalRegionName = this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionName!! }
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, true)
                } else {
                    showToast("当前商品没有逻辑区域")
                }
            },
            null
        )
    }

    private fun submitData() {
        var storageAttributeCode = ""
        if (storageAttribute != null) {
            storageAttributeCode = storageAttribute?.code!!
        }

        mPresenter.saveProductInfoMaintain(
            SaveProductInfoMaintainPost(
                this.productInfoMaintainQueryBean?.id!!,
                productCode,
                storageAttributeCode,
                this.ifErrorProne!!,
                this.smallProductPackingVO!!,
                this.largeProductPackingVO!!,
                this.lhkLogicalregionRelation!!,
                this.zjkLogicalregionRelation!!
            )
        )
    }

    /**
     * 初始化商品维护信息弹窗
     */
    private fun initCommodityMaintenanceDialog() {
        dialog_commodity_maintenance = Dialog(this)
        val view = LayoutInflater.from(this).inflate(R.layout.dialog_commodity_maintenance, null)
        dialog_commodity_maintenance.setContentView(view)
        val window = dialog_commodity_maintenance.window
        val windowparams = window?.attributes
        windowparams?.height = DensityUtil.dp2px(300f)
        windowparams?.width = DensityUtil.dp2px(800f)
        window?.attributes = windowparams

        val rv_commodity: RecyclerView = view.findViewById(R.id.rv_commodity)
        commodityMaintenanceAdapter = CommodityMaintenanceAdapter()
        rv_commodity.adapter = commodityMaintenanceAdapter
        rv_commodity.layoutManager = GridLayoutManager(this, 4)

        commodityMaintenanceAdapter?.setOnItemClickListener { _, _, position ->
            selectCurrentItem(position,2)
        }

        commodityMaintenanceAdapter!!.setNewData(commodityMaintenanceList)

        //关闭按钮
        val tvClose: TextView = view.findViewById(R.id.tv_close)
        tvClose.setOnClickListener { dialog_commodity_maintenance.hide() }
    }

    private fun initWholeCommodityMaintenanceDialog() {
        dialog_whole_commodity_maintenance = Dialog(this)
        val view = LayoutInflater.from(this).inflate(R.layout.dialog_commodity_maintenance, null)
        dialog_whole_commodity_maintenance.setContentView(view)
        val window = dialog_whole_commodity_maintenance.window
        val windowparams = window?.attributes
        windowparams?.height = DensityUtil.dp2px(300f)
        windowparams?.width = DensityUtil.dp2px(800f)
        window?.attributes = windowparams

        val rv_commodity: RecyclerView = view.findViewById(R.id.rv_commodity)
        wholeCommodityMaintenanceAdapter = CommodityMaintenanceAdapter()
        rv_commodity.adapter = wholeCommodityMaintenanceAdapter
        rv_commodity.layoutManager = GridLayoutManager(this, 4)
        //rv_commodity.addItemDecoration(AlternatelyBgItemDecoration())

        wholeCommodityMaintenanceAdapter?.setOnItemClickListener { _, _, position ->
            selectWholeCurrentItem(position,2)
        }

        wholeCommodityMaintenanceAdapter!!.setNewData(wholeCommodityMaintenanceList)
        Log.i("wholeMaintenanceList",wholeCommodityMaintenanceList.toString())
        //关闭按钮
        val tvClose: TextView = view.findViewById(R.id.tv_close)
        tvClose.setOnClickListener { dialog_whole_commodity_maintenance.hide() }
    }

    /**
     * scene 1 默认选中  2点击选择
     */
    private fun selectCurrentItem(position: Int, scene: Int) {
        val item: LogicalRegionProductInfoMaintainBean = commodityMaintenanceList[position]
        if(scene==2){
            item.isSelected = !item.isSelected
        }
        commodityMaintenanceList[position] = item
        commodityMaintenanceAdapter?.notifyItemRangeChanged(position, 1)
        var nameList = StringBuffer()
        var codeList = StringBuffer()
        commodityMaintenanceList.forEach {
            if (it.isSelected) {
                codeList.append("${it.dictCode},")
                nameList.append("${it.dictName},")
            }
        }
        et_logical_area.setText(nameList)
        this.lhkLogicalregionRelation?.logicalRegionCode = codeList.toString()
        this.lhkLogicalregionRelation?.logicalRegionName = nameList.toString()
    }
    /**
     * scene 1 默认选中  2点击选择
     */
    private fun selectWholeCurrentItem(position: Int, scene: Int) {
        val item: LogicalRegionProductInfoMaintainBean = wholeCommodityMaintenanceList[position]
        if(scene==2){
            item.isSelected = !item.isSelected
        }
        wholeCommodityMaintenanceList[position] = item
        wholeCommodityMaintenanceAdapter?.notifyItemRangeChanged(position, 1)
        var nameList = StringBuffer()
        var codeList = StringBuffer()
        wholeCommodityMaintenanceList.forEach {
            if (it.isSelected) {
                codeList.append("${it.dictCode},")
                nameList.append("${it.dictName},")
            }
        }
        et_whole_logical_area.setText(nameList)
        this.zjkLogicalregionRelation?.logicalRegionCode = codeList.toString()
        this.zjkLogicalregionRelation?.logicalRegionName = nameList.toString()
    }

    private fun switchProductPack(isSmall: Boolean) {
        if (isSmall) {
            this.isSmall = true
            tv_packet_length_tag.text = "小包装长"
            tv_packet_width_tag.text = "小包装宽"
            tv_packet_height_tag.text = "小包装高"
            tv_packet_volume_tag.text = "小包装体积"
            tv_packet_weight_tag.text = "小包装重量"
            if (productInfoMaintainQueryBean != null) {
                if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingLong != null) {
                    if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingLong != 0F) {
                        et_packet_length.setText(productInfoMaintainQueryBean?.smallProductPackingVO?.packingLong.toString())
                    } else {
                        et_packet_length.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingWide != null) {
                    if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingWide != 0F) {
                        et_packet_width.setText(productInfoMaintainQueryBean?.smallProductPackingVO?.packingWide.toString())
                    } else {
                        et_packet_width.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingHigh != null) {
                    if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingHigh != 0F) {
                        et_packet_height.setText(productInfoMaintainQueryBean?.smallProductPackingVO?.packingHigh.toString())
                    } else {
                        et_packet_height.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingVolume != null) {
                    if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingVolume != 0F) {
                        et_packet_volume.setText(productInfoMaintainQueryBean?.smallProductPackingVO?.packingVolume.toString())
                    } else {
                        et_packet_volume.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingWeight != null) {
                    if (productInfoMaintainQueryBean?.smallProductPackingVO?.packingWeight != 0F) {
                        et_packet_weight.setText(productInfoMaintainQueryBean?.smallProductPackingVO?.packingWeight.toString())
                    } else {
                        et_packet_weight.setText("")
                    }
                }
            }
        } else {
            this.isSmall = false
            tv_packet_length_tag.text = "大包装长"
            tv_packet_width_tag.text = "大包装宽"
            tv_packet_height_tag.text = "大包装高"
            tv_packet_volume_tag.text = "大包装体积"
            tv_packet_weight_tag.text = "大包装重量"
            if (productInfoMaintainQueryBean != null) {
                if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingLong != null) {
                    if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingLong != 0F) {
                        et_packet_length.setText(productInfoMaintainQueryBean?.largeProductPackingVO?.packingLong.toString())
                    } else {
                        et_packet_length.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingWide != null) {
                    if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingWide != 0F) {
                        et_packet_width.setText(productInfoMaintainQueryBean?.largeProductPackingVO?.packingWide.toString())
                    } else {
                        et_packet_width.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingHigh != null) {
                    if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingHigh != 0F) {
                        et_packet_height.setText(productInfoMaintainQueryBean?.largeProductPackingVO?.packingHigh.toString())
                    } else {
                        et_packet_height.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingVolume != null) {
                    if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingVolume != 0F) {
                        et_packet_volume.setText(productInfoMaintainQueryBean?.largeProductPackingVO?.packingVolume.toString())
                    } else {
                        et_packet_volume.setText("")
                    }
                }
                if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingWeight != null) {
                    if (productInfoMaintainQueryBean?.largeProductPackingVO?.packingWeight != 0F) {
                        et_packet_weight.setText(productInfoMaintainQueryBean?.largeProductPackingVO?.packingWeight.toString())
                    } else {
                        et_packet_weight.setText("")
                    }
                }
            }
        }
    }

    /**
     * 获取 PDA商品信息维护->信息查询 成功
     */
    override fun queryProductInfoMaintainSuccess(result: BaseResponseBean<ProductInfoMaintainQueryBean>) {
        if (result.code == 0) {
            this.productInfoMaintainQueryBean = result.result

            this.smallProductPackingVO = this.productInfoMaintainQueryBean?.smallProductPackingVO
            this.largeProductPackingVO = this.productInfoMaintainQueryBean?.largeProductPackingVO
            this.lhkLogicalregionRelation =
                this.productInfoMaintainQueryBean?.lhkLogicalregionRelation
            this.zjkLogicalregionRelation =
                this.productInfoMaintainQueryBean?.zjkLogicalregionRelation

            this.productInfoMaintainQueryBean?.storageAttributeList?.forEach {
                this.storageAttributeList.add(PickerDialogManager.SelectItemModel(it.name, it))
            }

            this.productInfoMaintainQueryBean?.abcTypeList?.forEach {
                this.abcTypeList.add(PickerDialogManager.SelectItemModel(it.dictName, it))
                this.wholeAbcTypeList.add(PickerDialogManager.SelectItemModel(it.dictName, it))
            }

            tv_product_name.text = this.productInfoMaintainQueryBean?.productName
            tv_product_number.text = this.productInfoMaintainQueryBean?.productCode
            tv_product_class.text = this.productInfoMaintainQueryBean?.largeCategory
            tv_dose.text = this.productInfoMaintainQueryBean?.dosageForm
            tv_storage_conditions.text = this.productInfoMaintainQueryBean?.storageConditions
            tv_product_piece_packing.text = this.productInfoMaintainQueryBean?.packingUnit
            tv_specifications.text = this.productInfoMaintainQueryBean?.specifications

            if (this.productInfoMaintainQueryBean?.ifErrorProne == 0) {
                rb_is_receive_no.isChecked = true
                rb_is_receive_yes.isChecked = false
                this.ifErrorProne = "0"
            } else {
                rb_is_receive_no.isChecked = false
                rb_is_receive_yes.isChecked = true
                this.ifErrorProne = "1"
            }

            tv_storage_room.text =
                this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomName
            tv_whole_storage_room.text =
                this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomName

            et_logical_area.setText(this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionName)
            et_whole_logical_area.setText(this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionName)

            et_inventory_limit.text =
                this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.stockUpper
            et_inventory_lower_limit.text =
                this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.stockLower
            et_whole_inventory_limit.text =
                this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.stockUpper
            et_whole_inventory_lower_limit.text =
                this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.stockLower

            if (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.goodspositionAbcType != null) {
                et_product_classification.setText(this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.goodspositionAbcTypeName)
                if (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode != null) {
                    var logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.storageRoomCode!!, this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.goodspositionAbcType!!, this.productInfoMaintainQueryBean!!.productCode)
                    if (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionName != null) { lhkLogicalregionRelation?.logicalRegionName = this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionName!! }
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, false)
                }
            }

            if (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.goodspositionAbcType != null) {
                et_whole_product_classification.setText(this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.goodspositionAbcTypeName)
                if (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode != null) {
                    var logicalRegionProductInfoMaintainPost = LogicalRegionProductInfoMaintainPost(this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.storageRoomCode!!, this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.goodspositionAbcType!!,this.productInfoMaintainQueryBean!!.productCode)
                    if (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionName != null) { logicalRegionProductInfoMaintainPost.logicalRegionName = this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionName!! }
                    mPresenter.logicalRegionProductInfoMaintain(logicalRegionProductInfoMaintainPost, true)
                }
            }

            this.productInfoMaintainQueryBean?.storageAttributeList?.forEach {
                if (it.code == this.productInfoMaintainQueryBean?.storageAttribute) {
                    et_storage_properties.setText(it.name)
                }
            }

            switchProductPack(true)
        }
    }

    /**
     * 请求库存上下限回调
     */
    override fun getUpperLowerProductInfoMaintainSuccess(
        getUpperLowerProductInfoMaintainBean: GetUpperLowerProductInfoMaintainBean,
        isWhole: Boolean
    ) {
        if (isWhole) {
            et_whole_inventory_limit.setText(getUpperLowerProductInfoMaintainBean.stockUpper)
            et_whole_inventory_lower_limit.setText(getUpperLowerProductInfoMaintainBean.stockLower)
        } else {
            et_inventory_limit.setText(getUpperLowerProductInfoMaintainBean.stockUpper)
            et_inventory_lower_limit.setText(getUpperLowerProductInfoMaintainBean.stockLower)
        }
    }

    /**
     * 请求逻辑区域回调
     */
    override fun logicalRegionProductInfoMaintainSuccess(
        logicalRegionProductInfoMaintainBean: ArrayList<LogicalRegionProductInfoMaintainBean>,
        isWhole: Boolean
    ) {
        if (isWhole) {
            et_whole_logical_area.setText("")
            wholeCommodityMaintenanceList = logicalRegionProductInfoMaintainBean
            //change string to list
            var logicalRegionCodelist: MutableList<String> = mutableListOf()
            if(this.productInfoMaintainQueryBean!=null){
                if(this.productInfoMaintainQueryBean?.zjkLogicalregionRelation!=null){
                    logicalRegionCodelist = (this.productInfoMaintainQueryBean?.zjkLogicalregionRelation?.logicalRegionCode?.split(",") as MutableList<String>?)!!
                }
            }
            if(logicalRegionCodelist.size>0){
                //Check whether the default value is selected
                for ((index,item) in wholeCommodityMaintenanceList.withIndex()){
                    for(itemz in logicalRegionCodelist){
                        if(item.dictName==itemz){
                            wholeCommodityMaintenanceList[index].isSelected = true
                            //调用选中操作，给默认值
                            this.selectWholeCurrentItem(index,1)
                        }
                    }
                }

            }
            wholeCommodityMaintenanceAdapter?.setNewData(wholeCommodityMaintenanceList)
        } else {
            et_logical_area.setText("")
            commodityMaintenanceList = logicalRegionProductInfoMaintainBean
            //change string to list
            var logicalRegionCodelist: MutableList<String> = mutableListOf()
            if(this.productInfoMaintainQueryBean!=null){
                if(this.productInfoMaintainQueryBean?.lhkLogicalregionRelation!=null){
                    logicalRegionCodelist = (this.productInfoMaintainQueryBean?.lhkLogicalregionRelation?.logicalRegionCode?.split(",") as MutableList<String>?)!!
                }
            }
            if(logicalRegionCodelist.size>0){
                //Check whether the default value is selected
                for ((index,item) in commodityMaintenanceList.withIndex()){
                    for(itemz in logicalRegionCodelist){
                        if(item.dictName==itemz){
                            commodityMaintenanceList[index].isSelected = true
                            //调用选中操作，给默认值
                            this.selectCurrentItem(index,1)
                        }
                    }
                }
            }
            commodityMaintenanceAdapter?.setNewData(commodityMaintenanceList)
        }
    }

    override fun saveProductInfoMaintainSuccess(string: String) {
        showToast("操作成功！")
        val intent = Intent()
        this.setResult(RESULT_OK, intent)
        this.finish()
    }

    override fun onDestroy(activity: SupportActivity?) {
        super.onDestroy(activity)
        dialog_commodity_maintenance.dismiss()
        dialog_whole_commodity_maintenance.dismiss()
    }

    /**
     * 自动计算提交
     */
    fun calVolume() {
        var length = 0F
        var width = 0F
        var height = 0F

        if (et_packet_length.text.isNotEmpty()) {
            length = et_packet_length.text.toString().toFloat()
        }
        if (et_packet_width.text.isNotEmpty()) {
            width = et_packet_width.text.toString().toFloat()
        }
        if (et_packet_height.text.isNotEmpty()) {
            height = et_packet_height.text.toString().toFloat()
        }
        val volume = length * width * height
        et_packet_volume.setText(volume.toInt().toString())
        if (isSmall) {
            smallProductPackingVO?.packingVolume = volume.toString().toFloat()
            largeProductPackingVO?.packingVolume = (smallProductPackingVO?.packingVolume!!) * (largeProductPackingVO?.packingNumber?.toInt()!!)
        } else {
            largeProductPackingVO?.packingVolume = volume.toString().toFloat()
            smallProductPackingVO?.packingVolume = (largeProductPackingVO?.packingVolume!!) / (largeProductPackingVO?.packingNumber?.toInt()!!)
        }
    }
}