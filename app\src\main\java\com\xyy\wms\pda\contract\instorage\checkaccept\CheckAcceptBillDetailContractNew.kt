package com.xyy.wms.pda.contract.instorage.checkaccept

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.bean.instorage.PackageBarCodeBean
import com.xyy.wms.pda.bean.instorage.ProductCodePost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDoSubmitPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownPost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPagePost
import com.xyy.wms.pda.bean.instorage.SecondLoginPost
import com.xyy.wms.pda.bean.instorage.SecondLoginResult
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderSavePost
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerBean
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerPost
import com.xyy.wms.pda.bean.instorage.PictureUploadResponse
import com.xyy.wms.pda.bean.instorage.PictureDeleteRequest
import com.xyy.wms.pda.bean.instorage.GoodsPicturesResult
import com.xyy.wms.pda.bean.instorage.UploadPicturePost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPageBean
import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.RequestBody

/**
 * 入库验收单
 */
interface CheckAcceptBillDetailContractNew {
    interface ICheckAcceptBillModelNew : IBaseModel {
      fun getDictListByNameAndType(dictName: String, dictType: String) : Observable<BaseResponseBean<List<DictListByNameAndType>>>
      fun selectProductCode(productCode: ProductCodePost) : Observable<BaseResponseBean<PackageBarCodeBean>>
      fun initCheckPage(initCheckPagePost: InitCheckPagePost) : Observable<BaseResponseBean<InitCheckPageBean>>
      fun turnDownCheckOrder(checkOrderTurnDownPost: CheckOrderTurnDownPost) : Observable<BaseResponseBean<CheckOrderTurnDownBean>>
      fun doSubmitCheckOrder(checkOrderDoSubmitPost: CheckOrderDoSubmitPost) : Observable<BaseResponseBean<String>>
      fun secondLogin(secondLoginPost: SecondLoginPost): Observable<BaseResponseBean<SecondLoginResult>>
      fun selectContainer(selectContainerPost : SelectContainerPost): Observable<BaseResponseBean<SelectContainerBean>>
      fun saveCheckOrder(checkOrderSavePost: CheckOrderSavePost): Observable<BaseResponseBean<String>>

      // 异常图片上传相关接口
      fun uploadPicture(files: UploadPicturePost): Observable<BaseResponseBean<List<String>>>
      fun deleteExceptionPictures(request: PictureDeleteRequest): Observable<BaseResponseBean<Boolean>>
      fun getExceptionPictures(businessId: String): Observable<BaseResponseBean<List<String>>>

      // 商品图片相关接口
      fun showGoosPicture(barcode: Map<String,String>): Observable<BaseResponseBean<List<GoodsPicturesResult>>>
    }
    interface ICheckAcceptBillViewNew : IBaseActivity {
      fun initCheckPageSuccess(initCheckPageBean: BaseResponseBean<InitCheckPageBean>)
      fun getDictListByNameAndTypeSuccess(dictListByNameAndType: BaseResponseBean<List<DictListByNameAndType>>)
      fun selectProductCodeSuccess(selectContainerBean: BaseResponseBean<PackageBarCodeBean>)
      fun turnDownCheckOrderSuccess(checkOrderTurnDownBean: BaseResponseBean<CheckOrderTurnDownBean>)
      fun doSubmitCheckOrderSuccess(checkOrderDoSubmitBean: BaseResponseBean<String>)
      fun secondLoginSuccess(secondLoginResult : BaseResponseBean<SecondLoginResult>)
      fun selectContainerSuccess(selectContainerBean: BaseResponseBean<SelectContainerBean>)
      fun saveCheckOrderSuccess(checkOrderSaveBean: BaseResponseBean<String>)

      // 异常图片上传相关回调
      fun uploadPictureSuccess(response: BaseResponseBean<List<String>>)
      fun deleteExceptionPicturesSuccess(response: BaseResponseBean<Boolean>)
      fun getExceptionPicturesSuccess(response: BaseResponseBean<List<String>>)

      // 商品图片相关回调
      fun showGoodsPictureSuccess(result: BaseResponseBean<List<GoodsPicturesResult>>)
    }
}
