# 图片上传接口调用文档

## 概述

本文档整理了异常图片上传弹窗提交时调用的接口详细信息，包括接口定义、参数模型、调用流程等。

## 1. 接口定义

### 1.1 基本信息
- **接口地址**: `POST /instock/wmsApp/checkOrder/upload`
- **请求格式**: `multipart/form-data`
- **响应格式**: `JSON`
- **功能描述**: 上传异常图片文件到服务器

### 1.2 接口声明

```kotlin
/**
 * APP->入库验收->11->上传图片
 * @param files 文件
 */
@POST("instock/wmsApp/checkOrder/upload")
fun uploadPicture(
    @Body files : UploadPicturePost
) : Observable<BaseResponseBean<List<String>>>
```

## 2. 请求参数

### 2.1 请求体模型

```kotlin
class UploadPicturePost(
    var file: MutableMap<Int, MutableList<PictureInfo>>
) : Serializable, IFormReq {
    override fun getRequestBody(): RequestBody {
        val resultBodyBuilder = MultipartBody.Builder()
        file.forEach { pictureInfoEntry ->
            val imageType = pictureInfoEntry.key
            val images = pictureInfoEntry.value
            for (index in 0 until images.size) {
                val item = images[index]
                if (item.isLocalImage()) {
                    val requestFile = RequestBody.create(MultipartBody.FORM, File(item.localUrl))
                    resultBodyBuilder.addFormDataPart("files", item.localUrl, requestFile)
                }
            }
        }
        resultBodyBuilder.setType(MultipartBody.FORM)
        return resultBodyBuilder.build()
    }
}
```

## 3. 响应数据

### 3.1 响应格式

```kotlin
BaseResponseBean<List<String>>
```

### 3.2 响应说明

| 字段 | 类型 | 说明 |
|------|------|------|
| result | List<String> | 上传成功的图片URL列表 |
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |

## 4. 业务场景调用流程

### 4.1 NewInspectionProductDetailActivity 场景


#### 弹窗内提交按钮逻辑

```kotlin
// 提交按钮 - 这是弹窗内的提交按钮
val tvSubmit: TextView = view.findViewById(R.id.tv_sumbit)
tvSubmit.setOnClickListener {
    var isNull = true
    val value = viewModel.imageMap.value!!
    val mutableList = value[currentType]
    mutableList?.forEach {
        if (it.localUrl != null) {
            isNull = false
        }
    }
    if (isNull) {
        showToast("请先上传异常图片再提交")
        return@setOnClickListener
    }
    mPresenter.uploadPicture(UploadPicturePost(viewModel.imageMap.value!!))
}
```

#### 上传成功回调

```kotlin
override fun uploadPictureSuccess(list: List<String>) {
    dialog_exception_picture.dismiss()  // 关闭弹窗
    showToast("上传异常图片成功")
    imgUrlsList = list  // 保存到成员变量，供后续业务保存使用
}

把这个图片上传提交的逻辑写入到ExceptionPictureDialog里面，使用里面的uploadPictures进行真实的图片API提交
API接口写入到 ApiInAcceptServiceNew 里面
成功后回调里面的数据要写入到 CheckBillDetailActivityNew 里面的imgUrlsList里面





