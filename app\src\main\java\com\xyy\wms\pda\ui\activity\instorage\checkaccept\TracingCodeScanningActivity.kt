package com.xyy.wms.pda.ui.activity.instorage.checkaccept

import android.os.Bundle
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.lwjfork.bus.LiveDataBus
import com.xyy.common.widget.DefaultItemDecoration
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.rxbus.RxBus
import com.xyy.utilslibrary.utils.DisplayUtils
import com.xyy.wms.pda.R
import com.xyy.wms.pda.constant.BusCode
import com.xyy.wms.pda.presenter.instorage.checkaccept.CheckAcceptBillDetailPresenterNew
import com.xyy.wms.pda.utils.CommonInfoUtils
import kotlinx.android.synthetic.main.activity_check_accept_bill_traceable_code_new.*

// 新增：iData 扫描相关
import android.os.IScanListener
import android.util.Log
import com.example.iscandemo.iScanInterface
import com.xyy.wms.pad.instorage.presenter.newinspection.TracingCodeScanningPresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckPermissionBystaffNumPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.ConfirmDetailBean
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderBean
import com.xyy.wms.pda.bean.instorage.checkaccept.FindPurchaseCodeScanOrderPost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPageBean
import com.xyy.wms.pda.bean.instorage.checkaccept.UnlockCodeScanOrder
import com.xyy.wms.pda.contract.instorage.checkaccept.TracingCodeScanningContract
import com.xyy.wms.pda.listener.LoginConfirmListener
import com.xyy.wms.pda.ui.adapter.instorage.checkaccept.TracingCodeScanningAdapter
import com.xyy.wms.pda.widget.dialog.SpecialDrugHint
import kotlinx.android.synthetic.main.activity_check_accept_bill_traceable_code_new.layout_recyclerview
import kotlinx.android.synthetic.main.operate_layout.tv_examiner
import kotlinx.android.synthetic.main.operate_layout.tv_examiner_key
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time
import kotlinx.android.synthetic.main.operate_layout.tv_reception_time_key

/**
 * 入库验收单-追溯码扫描
 */
class TracingCodeScanningActivity : BaseMVPCompatActivity<TracingCodeScanningPresenter>(), TracingCodeScanningContract.TracingCodeScanningContractView {
    private var checkPermissionBystaffNumBean : CheckPermissionBystaffNumBean? = null
    private var largeCategoryCode:String = ""
    private var mLocaleList = arrayListOf<ConfirmDetailBean>()
    private lateinit var mTracingCodeScanningAdapter : TracingCodeScanningAdapter
    private lateinit var initCheckPageBean : InitCheckPageBean

    // 新增：iData 扫描接口
    private var miScanInterface: iScanInterface? = null

    /**
     * 初始化传递数据
     */
    override fun initTransferData() {
        super.initTransferData()
        RxBus.get().register(this)
    }

    /**
     * 初始化Presenter
     */
    override fun initPresenter(): BasePresenter<*, *> {
        return TracingCodeScanningPresenter.newInstance()
    }

    /**
     * 获取布局ID
     */
    override fun getLayoutId(): Int {
        return R.layout.activity_check_accept_bill_traceable_code_new
    }

    /**
     * 初始化视图
     */
    override fun initView(savedInstanceState: Bundle?) {
        setupOperatorLayout()
        addListener()
        initRecyclerView()
        // 刷新页面
        LiveDataBus.get().with(BusCode.REFRESH_LIST).observe(this, Observer {
        })
    }

    /**
     * 设置操作员布局信息
     */
    private fun setupOperatorLayout() {
        initTitleBar(toolbar_check_accept, "")
        CommonInfoUtils.setOperationName(tv_examiner_key, "操作员：", tv_examiner)
        CommonInfoUtils.setOperationTime(tv_reception_time_key, "操作时间：", tv_reception_time)
    }

    /**
     * 初始化 iData 扫描功能
     */
    private fun initScan() {
        try {
            Class.forName("com.idata.IDataManager")
            if (miScanInterface == null) {
                miScanInterface = iScanInterface(this)
                miScanInterface!!.registerScan(miScanListener)
            }
        } catch (e: ClassNotFoundException) {
            Log.i("iData", "IDataManager 类未找到，设备可能不支持 iData")
        } catch (e: NoClassDefFoundError) {
            Log.i("iData", "iData设备不可用: ${e.message}")
        } catch (e: Exception) {
            Log.i("iData", "iScanInterface 初始化失败: ${e.message}")
        }
    }

    // 新增：iData 回调监听
    private val miScanListener = IScanListener { data, type, decodeTime, keyDownTime, imagePath ->
        val result = data ?: ""
        if (result.isEmpty()) {
            showToast("扫码失败")
            return@IScanListener
        }
        runOnUiThread {
            // 仅当验收码输入框有焦点时处理扫码
            if (ed_bar_code.hasFocus()) {
              ed_bar_code.setText(result)
              scanRegex(data)
            } else {
                // 无焦点：不做任何操作
            }
        }
    }
    /**
     * 添加按钮监听器
     */
    private fun addListener() {
        ed_bar_code_btn.setOnClickListener{
          scanRegex(ed_bar_code.text.toString())
        }
        btn_unLock.setOnClickListener { showSpecialDrugHint() }
        btn_submit.setOnClickListener {
            submitData()
        }

    }

    /**
     * 初始化RecyclerView列表
     */
    private fun initRecyclerView() {
        //控制列表的布局和项目之间的分隔线
        layout_recyclerview.layoutManager = LinearLayoutManager(mContext)
        val defaultItemDecoration = DefaultItemDecoration(this, DisplayUtils.dp2px(1f))
        defaultItemDecoration.setHasHeaderAndFooter(true)
        defaultItemDecoration.setLineColorResId(R.color.bg)
        layout_recyclerview.addItemDecoration(defaultItemDecoration)

        // 确保适配器已创建，使用回调处理删除
        if (!::mTracingCodeScanningAdapter.isInitialized) {
            mTracingCodeScanningAdapter = TracingCodeScanningAdapter(R.layout.item_tracing_code_scanning_goods) { position ->
                deleteCalculationScanData(position)
            }
            layout_recyclerview.adapter = mTracingCodeScanningAdapter
        }
        val data: MutableList<ConfirmDetailBean> = mutableListOf()
        mTracingCodeScanningAdapter.setNewData(data)

        mPresenter.findPurchaseCodeScanOrder(FindPurchaseCodeScanOrderPost(
            initCheckPageBean.buildingCode,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].channelCode,
            initCheckPageBean.checkOrderCode,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].orgCode,
            initCheckPageBean.ownerCode,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productBatchCode,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productCode,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productManufactureDate,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitBigSpecification,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitMediumSpecification,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].productValidDate,
            initCheckPageBean.purchaseCheckOrderDetailVos[0].lineNumber
        ))
    }

    /**
     * 提交数据
     */
    fun submitData() {
        if (checkPermissionBystaffNumBean != null) {
            mPresenter.unlockCodeScanOrder(
                UnlockCodeScanOrder(
                    initCheckPageBean.buildingCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].channelCode,
                    initCheckPageBean.checkOrderCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].orgCode,
                    initCheckPageBean.ownerCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productBatchCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productManufactureDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitBigSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitMediumSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productValidDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].lineNumber,
                    mLocaleList,
                    checkPermissionBystaffNumBean!!.id
                )
            )
        } else {
            mPresenter.confirmCodeScanOrder(
                ConfirmCodeScanOrderPost(
                    initCheckPageBean.buildingCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].channelCode,
                    initCheckPageBean.checkOrderCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].orgCode,
                    initCheckPageBean.ownerCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productBatchCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productManufactureDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitBigSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitMediumSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productValidDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].lineNumber,
                    mLocaleList
                )
            )
        }
    }

    /**
     * 显示特管商品校验对话框
     */
    private fun showSpecialDrugHint() {
        if (checkPermissionBystaffNumBean != null) {
            showToast("当前已经解锁！")
            return
        }
        SpecialDrugHint(mContext, SpecialDrugHint.TYPE_ACCEPT, LoginConfirmListener { userName, password ->
            val checkPermissionBystaffNumPost = CheckPermissionBystaffNumPost(userName, password, "btn:wms:purchaseCodeScanUnlockSub")
            mPresenter.checkPermissionBystaffNum(checkPermissionBystaffNumPost)
        }).show()
    }

    /**
     * 处理扫描数据（保留原有 Honeywell 扫描处理）
     */
    override fun scanData(data: String) {
        super.scanData(data)
        // 仅当验收码输入框有焦点时处理扫码
        if (ed_bar_code.hasFocus()) {
            ed_bar_code.setText(data)
            scanRegex(data)
        } else {
            // 无焦点：不做任何操作
        }
    }

    /**
     * 扫描数据正则校验
     */
    private fun scanRegex(scanData: String) {
        val regex = "[8][0-9]{19}".toRegex()
        if (scanData == "") {
            showToast("追溯码不能为空")
            return
        }
        if (largeCategoryCode != "202") {
            if (!regex.containsMatchIn(scanData)) {
                showToast("当前输入的追溯码不符合规范")
                return
            }
        }
        // 判断是否扫描重复
        mLocaleList.forEach {
            if (it.barcode == scanData) {
                showToast("当前输入的追溯码已经存在，不能重复扫描")
                return
            }
        }
        scanDataResult(scanData)
    }

    /**
     * 处理扫描结果
     */
    fun scanDataResult(str: String) {
        var type = ""
        var num = 0
        when (getClickType()) {
            1 -> {
                type = "小包装"
                num = tv_small_tag.text.toString().toInt()
            }
            2 -> {
                type = "中包装"
                num = tv_normal_tag.text.toString().toInt()
            }
            3 -> {
                type = "大包装"
                num = tv_big_tag.text.toString().toInt()
            }
        }

        if (num > tv_surplus.text.toString().toInt()) {
            showToast("当前包装数大于应扫数，请重新选择！")
            return
        }

        calculationScanData(num)

        mLocaleList.add(ConfirmDetailBean(str, getClickType(), type, num))
        mTracingCodeScanningAdapter.setNewData(mLocaleList)
    }

    /**
     * 计算扫描数据统计
     */
    private fun calculationScanData(num: Int) {
        var solid_scan_num = tv_solid_sweep.text.toString().toInt()
        var surplus_num = tv_surplus.text.toString().toInt()

        solid_scan_num += num;
        surplus_num -= num;

        tv_solid_sweep.text = solid_scan_num.toString()
        tv_surplus.text = surplus_num.toString()
    }

    /**
     * 获取当前选中的包装类型
     */
    private fun getClickType(): Int {
        return if (radio_small_tag.isChecked) {
            1
        } else if (radio_normal_tag.isChecked) {
            2
        } else if (radio_big_tag.isChecked) {
            3
        } else {
            0
        }
    }

    /**
     * 查询采购码扫描订单成功回调
     */
    override fun findPurchaseCodeScanOrderSuccess(result: BaseResponseBean<FindPurchaseCodeScanOrderBean>) {
        if (result.code == 0) {
            val findPurchaseCodeScanOrderBean = result.result
            tv_product_name.text = findPurchaseCodeScanOrderBean.productName
            tv_should_sweep.text = findPurchaseCodeScanOrderBean.needSweepCount
            tv_surplus.text = findPurchaseCodeScanOrderBean.needSweepCount
            tv_normal_tag.text = findPurchaseCodeScanOrderBean.productPackUnitMediumSpecification
            tv_big_tag.text = findPurchaseCodeScanOrderBean.productPackUnitBigSpecification
            largeCategoryCode = findPurchaseCodeScanOrderBean.largeCategoryCode
        } else {
            showToast(result.msg)
        }
    }

    /**
     * 确认码扫描订单成功回调
     */
    override fun confirmCodeScanOrderSuccess(result: BaseResponseBean<String>) {
        if (result.code == 0) {
            showToast(result.msg)
            finish()
        } else {
            showToast(result.msg)
        }
    }

    /**
     * 校验员工权限成功回调
     */
    override fun checkPermissionBystaffNumSuccess(result: BaseResponseBean<CheckPermissionBystaffNumBean>) {
        if (result.code == 0) {
            showToast(result.msg)
            this.checkPermissionBystaffNumBean = result.result
            // 解锁成功后直接提交
            mPresenter.unlockCodeScanOrder(
                UnlockCodeScanOrder(
                    initCheckPageBean.buildingCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].channelCode,
                    initCheckPageBean.checkOrderCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].orgCode,
                    initCheckPageBean.ownerCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productBatchCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productCode,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productManufactureDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitBigSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productPackUnitMediumSpecification,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].productValidDate,
                    initCheckPageBean.purchaseCheckOrderDetailVos[0].lineNumber,
                    mLocaleList,
                    checkPermissionBystaffNumBean!!.id
                )
            )
        } else {
            showToast(result.msg)
        }
    }

    /**
     * 解锁码扫描订单成功回调
     */
    override fun unlockCodeScanOrderSucces(result: BaseResponseBean<String>) {
        if (result.code == 0) {
            showToast(result.msg)
            finish()
        } else {
            showToast(result.msg)
        }
    }

    /**
     * 删除计算扫描数据
     */
    fun deleteCalculationScanData(num: Int) {
        val value = mLocaleList.get(num)?.count.toString().toInt()

        var solid_scan_num = tv_solid_sweep.text.toString().toInt()
        var surplus_num = tv_surplus.text.toString().toInt()

        solid_scan_num -= value;
        surplus_num += value;

        tv_solid_sweep.text = solid_scan_num.toString()
        tv_surplus.text = surplus_num.toString()
    }

    /**
     * Activity恢复时初始化扫描功能
     */
    override fun onResume() {
        super.onResume()
        // 新增：进入页面或返回时尝试初始化 iData 扫描
        initScan()
    }
}



