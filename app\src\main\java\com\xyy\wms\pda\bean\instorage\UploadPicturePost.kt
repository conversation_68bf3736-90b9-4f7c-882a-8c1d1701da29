package com.xyy.wms.pda.bean.instorage

import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okio.BufferedSink
import java.io.File
import android.util.Log

/**
 * 图片上传请求体模型
 * 根据接口文档定义的请求格式
 */
class UploadPicturePost(
    var file: MutableMap<Int, MutableList<PictureInfo>>
) : RequestBody() {

    private val actualRequestBody: RequestBody by lazy { buildRequestBody() }

    init {
        Log.d("ExceptionPictureDialog", "UploadPicturePost 构造函数被调用，文件映射大小: ${file.size}")
    }

    override fun contentType(): okhttp3.MediaType? {
        return actualRequestBody.contentType()
    }

    override fun contentLength(): Long {
        return actualRequestBody.contentLength()
    }

    override fun writeTo(sink: BufferedSink) {
        actualRequestBody.writeTo(sink)
    }

    private fun buildRequestBody(): RequestBody {
        val resultBodyBuilder = MultipartBody.Builder()
        resultBodyBuilder.setType(MultipartBody.FORM)

        Log.d("ExceptionPictureDialog", "开始构建请求体，文件映射大小: ${file.size}")

        file.forEach { pictureInfoEntry ->
            val imageType = pictureInfoEntry.key
            val images = pictureInfoEntry.value
            Log.d("ExceptionPictureDialog", "图片类型: $imageType, 图片数量: ${images.size}")

            for (index in 0 until images.size) {
                val item = images[index]
                Log.d("ExceptionPictureDialog", "处理图片 $index: 路径=${item.localPath}, isLocal=${item.isLocal}")

                if (item.isLocalImage()) {
                    val imageFile = File(item.localPath)
                    Log.d("ExceptionPictureDialog", "文件存在: ${imageFile.exists()}, 文件大小: ${imageFile.length()}")

                    if (imageFile.exists() && imageFile.length() > 0) {
                        // 创建文件的RequestBody，指定正确的媒体类型
                        val requestFile = RequestBody.create(
                            "image/*".toMediaTypeOrNull(),
                            imageFile
                        )
                        // 使用文件名而不是完整路径
                        val fileName = imageFile.name
                        Log.d("ExceptionPictureDialog", "添加文件到请求: $fileName")
                        resultBodyBuilder.addFormDataPart("files", fileName, requestFile)
                    } else {
                        Log.w("ExceptionPictureDialog", "文件不存在或大小为0: ${item.localPath}")
                    }
                } else {
                    Log.w("ExceptionPictureDialog", "不是本地图片，跳过: ${item.localPath}")
                }
            }
        }

        val requestBody = resultBodyBuilder.build()
        Log.d("ExceptionPictureDialog", "请求体构建完成")
        return requestBody
    }
}

/**
 * PictureInfo扩展方法
 */
fun PictureInfo.isLocalImage(): Boolean {
    return isLocal && localPath.isNotEmpty() && File(localPath).exists() && File(localPath).length() > 0
}
