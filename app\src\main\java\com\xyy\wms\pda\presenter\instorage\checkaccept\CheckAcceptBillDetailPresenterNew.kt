package com.xyy.wms.pda.presenter.instorage.checkaccept

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.wms.pda.bean.base.BaseResponseBean
import com.xyy.wms.pda.bean.common.DictListByNameAndType
import com.xyy.wms.pda.bean.instorage.InitCheckPageBean
import com.xyy.wms.pda.bean.instorage.PackageBarCodeBean
import com.xyy.wms.pda.bean.instorage.ProductCodePost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderDoSubmitPost
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownBean
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderTurnDownPost
import com.xyy.wms.pda.bean.instorage.checkaccept.InitCheckPagePost
import com.xyy.wms.pda.bean.instorage.SecondLoginPost
import com.xyy.wms.pda.bean.instorage.SecondLoginResult
import com.xyy.wms.pda.bean.instorage.checkaccept.CheckOrderSavePost
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerBean
import com.xyy.wms.pda.bean.instorage.checkaccept.SelectContainerPost
import com.xyy.wms.pda.bean.instorage.PictureUploadResponse
import com.xyy.wms.pda.bean.instorage.PictureDeleteRequest
import com.xyy.wms.pda.bean.instorage.GoodsPicturesResult
import com.xyy.wms.pda.bean.instorage.UploadPicturePost
import com.xyy.wms.pda.helper.SimpleErrorConsumer
import com.xyy.wms.pda.helper.SimpleSuccessConsumer
import com.xyy.wms.pda.contract.instorage.checkaccept.CheckAcceptBillDetailContractNew
import com.xyy.wms.pda.model.instorage.checkaccept.CheckAcceptBillDetailModelNew
import okhttp3.MultipartBody
import okhttp3.RequestBody

/**
 * 入库验收单（New）Presenter：使用新 Model/Api，直接返回新 Bean
 */
class CheckAcceptBillDetailPresenterNew : BasePresenter<CheckAcceptBillDetailContractNew.ICheckAcceptBillModelNew, CheckAcceptBillDetailContractNew.ICheckAcceptBillViewNew>() {
    override fun getModel(): CheckAcceptBillDetailModelNew {
        return CheckAcceptBillDetailModelNew.newInstance()
    }
    companion object {
        fun newInstance(): CheckAcceptBillDetailPresenterNew = CheckAcceptBillDetailPresenterNew()
    }
    fun getDictListByNameAndType(dictName: String, dictType: String) {
      mRxManager.register(mIModel.getDictListByNameAndType(dictName, dictType).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<DictListByNameAndType>>>(mIView,"加载中") {
        override fun onSuccess(t: BaseResponseBean<List<DictListByNameAndType>>) {
          mIView.getDictListByNameAndTypeSuccess(t)
        }
      }, SimpleErrorConsumer(mIView)))
    }
    fun selectProductCode(productCodePost: ProductCodePost) {
      mRxManager.register(mIModel.selectProductCode(productCodePost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<PackageBarCodeBean>>(mIView,"加载中") {
          override fun isSuccess(code: Int): Boolean {
            return code == 0 || code == 2001  // 允许 code 0 和 2001 都通过
          }
          override fun onSuccess(t: BaseResponseBean<PackageBarCodeBean>) {
            mIView.selectProductCodeSuccess(t)
          }
        }, SimpleErrorConsumer(mIView)))
    }
    fun initCheckPage(initCheckPagePost: InitCheckPagePost) {
      mRxManager.register(mIModel.initCheckPage(initCheckPagePost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<InitCheckPageBean>>(mIView, "请求中···") {
          override fun onSuccess(responseBean : BaseResponseBean<InitCheckPageBean>) {
            mIView.initCheckPageSuccess(responseBean)
          }
        }, SimpleErrorConsumer(mIView))
      )
    }
    fun turnDownCheckOrder(checkOrderTurnDownPost: CheckOrderTurnDownPost) {
      mRxManager.register(mIModel.turnDownCheckOrder(checkOrderTurnDownPost).subscribe(object : SimpleSuccessConsumer<BaseResponseBean<CheckOrderTurnDownBean>>(mIView,"加载中") {
        override fun onSuccess(t: BaseResponseBean<CheckOrderTurnDownBean>) {
          mIView.turnDownCheckOrderSuccess(t)
        }
      }, SimpleErrorConsumer(mIView)))
    }
    fun doSubmitCheckOrder(checkOrderDoSubmitPost: CheckOrderDoSubmitPost) {
      mRxManager.register(mIModel.doSubmitCheckOrder(checkOrderDoSubmitPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<String>>(mIView,"加载中") {
          override fun isSuccess(code: Int): Boolean {
            return code == 0 || code == 1 || code == 20002 || code == 20003 || code == 20005
          }
          override fun onSuccess(t: BaseResponseBean<String>) {
            mIView.doSubmitCheckOrderSuccess(t)
          }
        }, SimpleErrorConsumer(mIView)))
    }
    fun secondLogin(secondLoginPost: SecondLoginPost) {
      mRxManager.register(mIModel.secondLogin(secondLoginPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<SecondLoginResult>>(mIView) {
          override fun onSuccess(requestBaseBean: BaseResponseBean<SecondLoginResult>) {
            mIView.secondLoginSuccess(requestBaseBean)
          }
        }, SimpleErrorConsumer(mIView)))
    }
    fun selectContainer(selectContainerPost: SelectContainerPost) {
      mRxManager.register(mIModel.selectContainer(selectContainerPost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<SelectContainerBean>>(mIView, "请求中···") {
          override fun onSuccess(responseBean : BaseResponseBean<SelectContainerBean>) {
            mIView.selectContainerSuccess(responseBean)
          }
        }, SimpleErrorConsumer(mIView))
      )
    }
    fun saveCheckOrder(checkOrderSavePost: CheckOrderSavePost) {
      mRxManager.register(mIModel.saveCheckOrder(checkOrderSavePost)
        .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<String>>(mIView,"保存中") {
          override fun isSuccess(code: Int): Boolean {
            return code == 0 || code == 2001
          }
          override fun onSuccess(t: BaseResponseBean<String>) {
            mIView.saveCheckOrderSuccess(t)
          }
        }, SimpleErrorConsumer(mIView)))
    }

    // 异常图片上传相关方法
    fun uploadPicture(files: UploadPicturePost) {
        android.util.Log.d("ExceptionPictureDialog", "CheckAcceptPresenter.uploadPicture() 被调用")
        mRxManager.register(mIModel.uploadPicture(files)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<String>>>(mIView, "上传中") {
                override fun onSuccess(t: BaseResponseBean<List<String>>) {
                    android.util.Log.d("ExceptionPictureDialog", "上传成功回调")
                    mIView.uploadPictureSuccess(t)
                }
            }, SimpleErrorConsumer(mIView)))
    }

    fun deleteExceptionPictures(request: PictureDeleteRequest) {
        mRxManager.register(mIModel.deleteExceptionPictures(request)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<Boolean>>(mIView, "删除中") {
                override fun onSuccess(t: BaseResponseBean<Boolean>) {
                    mIView.deleteExceptionPicturesSuccess(t)
                }
            }, SimpleErrorConsumer(mIView)))
    }

    fun getExceptionPictures(businessId: String) {
        mRxManager.register(mIModel.getExceptionPictures(businessId)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<String>>>(mIView, "加载中") {
                override fun onSuccess(t: BaseResponseBean<List<String>>) {
                    mIView.getExceptionPicturesSuccess(t)
                }
            }, SimpleErrorConsumer(mIView)))
    }

    fun showGoosPicture(barcode: Map<String,String>) {
        mRxManager.register(mIModel.showGoosPicture(barcode)
            .subscribe(object : SimpleSuccessConsumer<BaseResponseBean<List<GoodsPicturesResult>>>(mIView) {
                override fun onSuccess(requestBaseBean: BaseResponseBean<List<GoodsPicturesResult>>) {
                    mIView.showGoodsPictureSuccess(requestBaseBean)
                }
            }, SimpleErrorConsumer(mIView)))
    }
}



