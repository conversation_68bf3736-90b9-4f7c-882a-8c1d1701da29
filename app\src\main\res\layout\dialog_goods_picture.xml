<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:orientation="vertical">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#000000"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="商品图片"
            android:textColor="@color/color_write"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="5dp"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="@color/color_write"
            android:contentDescription="关闭" />

    </RelativeLayout>

    <!-- ViewPager2轮播图 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- 指示器 -->
    <LinearLayout
        android:id="@+id/ll_indicators"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:orientation="horizontal" />

    <!-- 图片计数显示 -->
    <TextView
        android:id="@+id/tv_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="20dp"
        android:text="1/1"
        android:textColor="@color/color_write"
        android:textSize="14sp" />

</LinearLayout>
